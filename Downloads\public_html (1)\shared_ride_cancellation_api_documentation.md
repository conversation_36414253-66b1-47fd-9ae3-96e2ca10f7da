# Shared Ride Cancellation API Documentation

## Overview

The Shared Ride Cancellation API allows users to opt out of shared rides after both users have agreed to share. When one user cancels, the shared ride is dissolved and both rides become individual rides again.

## Base URL

```
https://your-domain.com/api/user/ride-share
```

## Authentication

All endpoints require authentication using a Bearer token.

```
Authorization: Bearer {your_access_token}
```

## Shared Ride Cancellation Endpoint

### Cancel/Opt Out of Shared Ride

Allows a user to cancel their participation in a shared ride after it has been accepted by both parties.

**Endpoint:** `POST /cancel`

**Authentication Required:** Yes

**Request Headers:**
```
Authorization: Bearer {your_access_token}
Content-Type: application/json
```

**Request Body:**

```json
{
  "shared_ride_id": 123,
  "cancel_reason": "Change of plans - need to travel at different time"
}
```

**Request Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| shared_ride_id | Integer | Yes | ID of the shared ride to cancel |
| cancel_reason | String | Yes | Reason for canceling (max 255 characters) |

**Success Response:**

```json
{
  "status": "success",
  "message": "success",
  "data": {
    "shared_ride_id": 123,
    "your_ride": {
      "id": 456,
      "uid": "RIDE123456",
      "pickup_location": "Downtown Mall",
      "destination": "Airport Terminal 1",
      "amount": "25.00",
      "is_shared": false,
      "shared_ride_id": 0,
      "status": 0
    },
    "other_user": {
      "id": 789,
      "name": "John Doe",
      "username": "johndoe"
    }
  },
  "notification": ["You have successfully opted out of the shared ride"]
}
```

**Error Responses:**

**Validation Errors:**
```json
{
  "status": "error",
  "message": "error",
  "notification": [
    "The shared ride id field is required",
    "The cancel reason field is required"
  ]
}
```

**Shared Ride Not Found:**
```json
{
  "status": "error",
  "message": "error",
  "notification": ["Shared ride not found or not eligible for cancellation"]
}
```

**Cannot Cancel After Ride Started:**
```json
{
  "status": "error",
  "message": "error",
  "notification": ["Cannot cancel shared ride after it has started"]
}
```

**Invalid Data:**
```json
{
  "status": "error",
  "message": "error",
  "notification": ["Invalid shared ride data"]
}
```

## Cancellation Rules

### When Can Users Cancel?

1. **Shared ride must be in ACCEPTED status** (status = 1)
2. **Neither ride has started** (not in RUNNING, END, or COMPLETED status)
3. **User must be a participant** in the shared ride
4. **Both rides must exist** and be valid

### What Happens When a User Cancels?

1. **Shared ride status** changes to CANCELED (status = 9)
2. **Both individual rides** are updated:
   - `is_shared` set to `false`
   - `shared_ride_id` set to `0`
3. **Other user receives notification** via email, SMS, and push notification
4. **Real-time updates** sent via Pusher to both users
5. **Both rides become individual** and can proceed independently

## Notifications

### Email Notification
The other user receives an email with:
- Canceling user's username
- Cancellation reason
- Ride ID for reference
- Information about continuing as individual ride

### SMS Notification
Short message with:
- Canceling user's username
- Cancellation reason
- Ride ID

### Push Notification
Real-time notification with:
- Canceling user's username
- Cancellation reason

### Real-time Updates (Pusher)
Both users receive real-time updates on:
- User-specific channels: `user-{user_id}`
- Ride-specific channels for both rides

## Integration Examples

### Cancel Shared Ride
```javascript
// JavaScript example
const cancelSharedRide = async (sharedRideId, reason) => {
  try {
    const response = await fetch('/api/user/ride-share/cancel', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer ' + token,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        shared_ride_id: sharedRideId,
        cancel_reason: reason
      })
    });
    
    const data = await response.json();
    
    if (data.status === 'success') {
      console.log('Shared ride canceled successfully');
      // Update UI to show individual ride
    } else {
      console.error('Error:', data.notification);
    }
  } catch (error) {
    console.error('Network error:', error);
  }
};
```

### Listen for Cancellation Events
```javascript
// Pusher integration example
const pusher = new Pusher('your-pusher-key');
const channel = pusher.subscribe('user-' + userId);

channel.bind('shared-ride-canceled', function(data) {
  console.log('Shared ride canceled:', data);
  // Update UI to show that shared ride was canceled
  // Show notification to user
  // Update ride status to individual
});
```

## Mobile App Integration

### UI Considerations

1. **Cancel Button**: Show in shared ride details when ride is in accepted status
2. **Confirmation Dialog**: Ask user to confirm cancellation and provide reason
3. **Reason Input**: Text field for cancellation reason (required)
4. **Real-time Updates**: Listen for cancellation events from other user
5. **Status Updates**: Update ride display from "Shared" to "Individual"

### Recommended Flow

1. User opens shared ride details
2. User taps "Opt Out" or "Cancel Shared Ride" button
3. App shows confirmation dialog with reason input
4. User enters reason and confirms
5. App sends cancellation request
6. App updates UI based on response
7. App listens for real-time updates

## Error Handling

### Common Scenarios

1. **Network Issues**: Implement retry logic
2. **Invalid Shared Ride**: Check if shared ride still exists
3. **Already Canceled**: Handle gracefully if already canceled by other user
4. **Ride Started**: Inform user that cancellation is no longer possible

### Best Practices

1. **Validate Input**: Check shared_ride_id and reason before sending
2. **Handle Real-time**: Listen for cancellation events from other user
3. **Update UI**: Immediately update interface after successful cancellation
4. **Error Messages**: Show clear, user-friendly error messages
5. **Confirmation**: Always confirm before canceling

## Related Endpoints

- `GET /active` - Get active shared rides (excludes canceled)
- `GET /details/{id}` - Get shared ride details
- `POST /chat/send/{sharedRideId}` - Send message (disabled after cancellation)

---

This documentation covers the shared ride cancellation functionality that allows users to opt out of shared rides after acceptance. The feature ensures both users are properly notified and their rides continue as individual rides.
