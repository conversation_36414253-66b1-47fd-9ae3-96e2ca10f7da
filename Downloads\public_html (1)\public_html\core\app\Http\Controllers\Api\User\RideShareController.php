<?php

namespace App\Http\Controllers\Api\User;

use App\Constants\Status;
use App\Events\Ride as EventsRide;
use App\Events\NewRide;
use App\Http\Controllers\Controller;
use App\Models\Ride;
use App\Models\RideShareRequest;
use App\Models\SharedRide;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

use Illuminate\Support\Facades\Validator;

class RideShareController extends Controller
{
    /**
     * Search for nearby users going to similar destinations
     */
    public function searchNearbyUsers(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ride_id' => 'required|exists:rides,id',
            'max_distance' => 'nullable|numeric|min:1|max:10',
        ]);

        if ($validator->fails()) {
            return apiResponse("validation_error", "error", $validator->errors()->all());
        }

        $user = Auth::user();
        $ride = Ride::where('user_id', $user->id)
            ->whereIn('status', [Status::RIDE_PENDING, Status::RIDE_ACTIVE])
            ->notShared()
            ->find($request->ride_id);

        if (!$ride) {
            $notify[] = 'Ride not found or not eligible for sharing';
            return apiResponse("not_found", "error", $notify);
        }

        // Maximum distance in kilometers (default: 5km)
        $maxDistance = $request->max_distance ?? 5;

        $userRide = Ride::find($request->ride_id);
        $maxPickupDistance = $request->max_distance ?? 5;

        // Get potential nearby rides
        $nearbyRides = Ride::with('user')
            ->whereIn('status', [Status::RIDE_PENDING, Status::RIDE_ACTIVE])
            ->notShared()
            ->where('id', '!=', $request->ride_id)
            ->where('user_id', '!=', Auth::id())
            ->get()
            ->filter(function($ride) use ($userRide, $maxPickupDistance) {
                $pickupDistance = $this->calculateDistance(
                    $userRide->pickup_latitude,
                    $userRide->pickup_longitude,
                    $ride->pickup_latitude,
                    $ride->pickup_longitude
                );

                $destinationDistance = $this->calculateDistance(
                    $userRide->destination_latitude,
                    $userRide->destination_longitude,
                    $ride->destination_latitude,
                    $ride->destination_longitude
                );

                return $pickupDistance <= $maxPickupDistance && $destinationDistance <= 5;
            })
            ->values(); // Reset array keys after filtering

        // Check if user already has pending requests with these users
        $pendingRequests = RideShareRequest::where(function($query) use ($user) {
                $query->where('requester_id', $user->id)
                    ->orWhere('recipient_id', $user->id);
            })
            ->pending()
            ->get();


        // Convert to collection for pagination
        $collection = collect($nearbyRides);

        // Get pagination parameters
        $perPage = getPaginate();
        $page = request()->page ?? 1;
        $total = $collection->count();

        // Slice the collection for pagination
        $items = $collection->slice(($page - 1) * $perPage, $perPage)->values();

        // Create a custom paginator
        $paginator = new \Illuminate\Pagination\LengthAwarePaginator(
            $items,
            $total,
            $perPage,
            $page,
            ['path' => request()->url(), 'query' => request()->query()]
        );

        // Format the nearby users data
        $nearbyUsers = $paginator->map(function ($nearbyRide) {
            return [
                'user' => [
                    'id' => $nearbyRide->user->id,
                    'name' => $nearbyRide->user->fullname,
                    'username' => $nearbyRide->user->username,
                    'image' => $nearbyRide->user->image,
                ],
                'ride' => [
                    'id' => $nearbyRide->id,
                    'pickup_latitude' =>$nearbyRide->pickup_latitude,
                    'pickup_longitude' =>$nearbyRide->pickup_longitude,
                    'pickup_location' => $nearbyRide->pickup_location,

                    'destination_latitude' => $nearbyRide->destination_latitude,
                    'destination_longitude' => $nearbyRide->destination_longitude,
                    'destination' => $nearbyRide->destination,

                    #'distance' => round($nearbyRide->distance, 2) . ' km',
                ],
            ];
        });

        $notify[] = $paginator->count() > 0 ? 'Nearby users found' : 'No nearby users found';

        // Format the paginator data for the response
        $paginationData = [
            'current_page' => $paginator->currentPage(),
            'last_page' => $paginator->lastPage(),
            'per_page' => $paginator->perPage(),
            'total' => $paginator->total(),
            'has_more_pages' => $paginator->hasMorePages(),
        ];

        return apiResponse("nearby_users", "success", $notify, [
            'nearby_users' => $nearbyUsers,
            'pagination' => $paginationData,
            'user_image_path' => getFilePath('user')
        ]);
    }

    /**
     * Send a ride share request to another user
     */
    public function sendRequest(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ride_id' => 'required|exists:rides,id',
            'recipient_id' => 'required|exists:users,id',
            'recipient_ride_id' => 'required|exists:rides,id',
        ]);

        if ($validator->fails()) {
            return apiResponse("validation_error", "error", $validator->errors()->all());
        }

        $user = Auth::user();
        $ride = Ride::where('user_id', $user->id)
            ->whereIn('status', [Status::RIDE_PENDING, Status::RIDE_ACTIVE])
            ->notShared()
            ->find($request->ride_id);

        if (!$ride) {
            $notify[] = 'Ride not found or not eligible for sharing';
            return apiResponse("not_found", "error", $notify);
        }

        // Get the specific recipient ride
        $recipientRide = Ride::where('id', $request->recipient_ride_id)
            ->where('user_id', $request->recipient_id)
            ->whereIn('status', [Status::RIDE_PENDING, Status::RIDE_ACTIVE])
            ->notShared()
            ->with('service')
            ->first();

        if (!$recipientRide) {
            $notify[] = 'The selected ride is not found or not eligible for sharing';
            return apiResponse("invalid_recipient_ride", "error", $notify);
        }
        
        // Check if there's already a pending request
        /*
        $existingRequest = RideShareRequest::where(function ($query) use ($user, $request) {
                $query->where('requester_id', $user->id)
                    ->where('recipient_id', $request->recipient_id);
            })
            ->orWhere(function ($query) use ($user, $request) {
                $query->where('requester_id', $request->recipient_id)
                    ->where('recipient_id', $user->id);
            })
            ->pending()
            ->first();

        if ($existingRequest) {
            $notify[] = 'A ride share request already exists between you and this user';
            return apiResponse("request_exists", "error", $notify);
        }*/

        // Create the ride share request
        $shareRequest = new RideShareRequest();
        $shareRequest->requester_id = $user->id;
        $shareRequest->recipient_id = $request->recipient_id;
        $shareRequest->ride_id = $ride->id;
        $shareRequest->recipient_ride_id = $request->recipient_ride_id;
        $shareRequest->status = RideShareRequest::STATUS_PENDING;
        $shareRequest->save();

        // Send notification to recipient
        $recipient = User::find($request->recipient_id);
        $shortCodes = [
            'username' => $user->username,
            'ride_pickup' => $ride->pickup_location,
            'ride_destination' => $ride->destination,
        ];
        notify($recipient, 'RIDE_SHARE_REQUEST', $shortCodes);

        // Send real-time Pusher notification
        initializePusher();
        $eventData = [
            'request' => $shareRequest,
            'requester' => [
                'id' => $user->id,
                'name' => $user->fullname,
                'username' => $user->username,
                'image' => $user->image,
            ],
            'ride' => [
                'id' => $ride->id,
                'pickup_location' => $ride->pickup_location,
                'destination' => $ride->destination,
            ],
            'user_image_path' => getFilePath('user'),
        ];

        // Create a custom channel for the recipient
        $channelName = 'user-' . $recipient->id;
        event(new NewRide($channelName, $eventData, 'ride-share-request'));

        $notify[] = 'Ride share request sent successfully';
        return apiResponse("request_sent", "success", $notify);
    }

    /**
     * Get all pending ride share requests for the authenticated user
     */
    public function getRequests()
    {
        $user = Auth::user();

        // Get requests where user is the recipient
        $receivedRequests = RideShareRequest::with(['requester', 'ride'])
            ->where('recipient_id', $user->id)
            ->pending()
            ->get()
            ->map(function ($request) {
                return [
                    'id' => $request->id,
                    'type' => 'received',
                    'user' => [
                        'id' => $request->requester->id,
                        'name' => $request->requester->fullname,
                        'username' => $request->requester->username,
                        'image' => $request->requester->image,
                    ],
                    'ride' => [
                        'id' => $request->ride->id,
                        'pickup_location' => $request->ride->pickup_location,
                        'destination' => $request->ride->destination,
                    ],
                    'created_at' => $request->created_at,
                ];
            });

        // Get requests where user is the requester
        $sentRequests = RideShareRequest::with(['recipient', 'ride'])
            ->where('requester_id', $user->id)
            ->pending()
            ->get()
            ->map(function ($request) {
                return [
                    'id' => $request->id,
                    'type' => 'sent',
                    'user' => [
                        'id' => $request->recipient->id,
                        'name' => $request->recipient->fullname,
                        'username' => $request->recipient->username,
                        'image' => $request->recipient->image,
                    ],
                    'ride' => [
                        'id' => $request->ride->id,
                        'pickup_location' => $request->ride->pickup_location,
                        'destination' => $request->ride->destination,
                    ],
                    'created_at' => $request->created_at,
                ];
            });

        $requests = $receivedRequests->merge($sentRequests)->sortByDesc('created_at')->values();

        $notify[] = 'Ride share requests';
        return apiResponse("ride_share_requests", "success", $notify, [
            'requests' => $requests,
            'user_image_path' => getFilePath('user'),
        ]);
    }

    /**
     * Accept a ride share request
     */
    public function acceptRequest(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'request_id' => 'required|exists:ride_share_requests,id',
        ]);

        if ($validator->fails()) {
            return apiResponse("validation_error", "error", $validator->errors()->all());
        }

        $user = Auth::user();

        // Get the ride share request
        $shareRequest = RideShareRequest::with(['requester', 'ride', 'recipientRide'])
            ->where('recipient_id', $user->id)
            ->pending()
            ->find($request->request_id);

        if (!$shareRequest) {
            $notify[] = 'Ride share request not found or already processed';
            return apiResponse("not_found", "error", $notify);
        }

        // Get the recipient's ride from the request
        $recipientRide = $shareRequest->recipientRide;

        if (!$recipientRide || $recipientRide->user_id != $user->id || !in_array($recipientRide->status, [Status::RIDE_PENDING, Status::RIDE_ACTIVE]) || $recipientRide->is_shared) {
            $notify[] = 'The specified ride is not available for sharing';
            return apiResponse("invalid_ride", "error", $notify);
        }

        // Create shared ride record
        $sharedRide = new SharedRide();
        $sharedRide->primary_ride_id = $shareRequest->ride_id;
        $sharedRide->secondary_ride_id = $recipientRide->id;
        $sharedRide->status = SharedRide::STATUS_ACCEPTED;
        $sharedRide->save();

        // Update both rides
        $requesterRide = Ride::find($shareRequest->ride_id);
        $requesterRide->is_shared = true;
        $requesterRide->shared_ride_id = $sharedRide->id;
        $requesterRide->save();

        $recipientRide->is_shared = true;
        $recipientRide->shared_ride_id = $sharedRide->id;
        $recipientRide->save();

        // Update request status
        $shareRequest->status = RideShareRequest::STATUS_ACCEPTED;
        $shareRequest->save();

        // Send notification to requester
        $shortCodes = [
            'username' => $user->username,
            'ride_pickup' => $recipientRide->pickup_location,
            'ride_destination' => $recipientRide->destination,
        ];
        notify($shareRequest->requester, 'RIDE_SHARE_ACCEPTED', $shortCodes);

        // Send real-time Pusher notification
        initializePusher();
        $eventData = [
            'shared_ride' => $sharedRide,
            'accepter' => [
                'id' => $user->id,
                'name' => $user->fullname,
                'username' => $user->username,
                'image' => $user->image,
            ],
            'ride' => [
                'id' => $recipientRide->id,
                'pickup_location' => $recipientRide->pickup_location,
                'destination' => $recipientRide->destination,
            ],
            'user_image_path' => getFilePath('user'),
        ];

        // Create a custom channel for the requester
        $channelName = 'user-' . $shareRequest->requester_id;
        event(new NewRide($channelName, $eventData, 'ride-share-accepted'));

        // Also emit an event on the ride channel for both rides
        event(new EventsRide($requesterRide, 'ride-share-accepted', $eventData));
        event(new EventsRide($recipientRide, 'ride-share-accepted', $eventData));

        $notify[] = 'Ride share request accepted successfully';
        return apiResponse("request_accepted", "success", $notify, [
            'shared_ride_id' => $sharedRide->id,
        ]);
    }

    /**
     * Reject a ride share request
     */
    public function rejectRequest(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'request_id' => 'required|exists:ride_share_requests,id',
        ]);

        if ($validator->fails()) {
            return apiResponse("validation_error", "error", $validator->errors()->all());
        }

        $user = Auth::user();

        // Get the ride share request
        $shareRequest = RideShareRequest::with('requester')
            ->where('recipient_id', $user->id)
            ->pending()
            ->find($request->request_id);

        if (!$shareRequest) {
            $notify[] = 'Ride share request not found or already processed';
            return apiResponse("not_found", "error", $notify);
        }

        // Update request status
        $shareRequest->status = RideShareRequest::STATUS_REJECTED;
        $shareRequest->save();

        // Send notification to requester
        $shortCodes = [
            'username' => $user->username,
        ];
        notify($shareRequest->requester, 'RIDE_SHARE_REJECTED', $shortCodes);

        // Send real-time Pusher notification
        initializePusher();
        $eventData = [
            'request_id' => $shareRequest->id,
            'rejecter' => [
                'id' => $user->id,
                'name' => $user->fullname,
                'username' => $user->username,
            ],
            'user_image_path' => getFilePath('user'),
        ];

        // Create a custom channel for the requester
        $channelName = 'user-' . $shareRequest->requester_id;
        event(new NewRide($channelName, $eventData, 'ride-share-rejected'));

        // Also emit an event on the ride channel
        $requesterRide = $shareRequest->ride;
        if ($requesterRide) {
            event(new EventsRide($requesterRide, 'ride-share-rejected', $eventData));
        }

        $notify[] = 'Ride share request rejected';
        return apiResponse("request_rejected", "success", $notify);
    }

    /**
     * Get shared ride details
     */
    public function getSharedRideDetails($id)
    {
        $user = Auth::user();

        // Get the shared ride
        $sharedRide = SharedRide::with(['primaryRide.user', 'secondaryRide.user'])
            ->where(function ($query) use ($user) {
                $query->whereHas('primaryRide', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                })->orWhereHas('secondaryRide', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                });
            })
            ->find($id);

        if (!$sharedRide) {
            $notify[] = 'Shared ride not found';
            return apiResponse("not_found", "error", $notify);
        }

        // Determine the partner's ride and user
        $userRide = null;
        $partnerRide = null;
        $partnerUser = null;

        if ($sharedRide->primaryRide->user_id == $user->id) {
            $userRide = $sharedRide->primaryRide;
            $partnerRide = $sharedRide->secondaryRide;
            $partnerUser = $sharedRide->secondaryRide->user;
        } else {
            $userRide = $sharedRide->secondaryRide;
            $partnerRide = $sharedRide->primaryRide;
            $partnerUser = $sharedRide->primaryRide->user;
        }

        $sharedRideDetails = [
            'id' => $sharedRide->id,
            'status' => $sharedRide->status,
            'user_ride' => [
                'id' => $userRide->id,
                'pickup_location' => $userRide->pickup_location,
                'pickup_latitude' => $userRide->pickup_latitude,
                'pickup_longitude' => $userRide->pickup_longitude,
                'destination' => $userRide->destination,
                'destination_latitude' => $userRide->destination_latitude,
                'destination_longitude' => $userRide->destination_longitude,
                'amount' => $userRide->amount,
                'status' => $userRide->status,
            ],
            'partner' => [
                'id' => $partnerUser->id,
                'name' => $partnerUser->fullname,
                'username' => $partnerUser->username,
                'image' => $partnerUser->image,
            ],
            'partner_ride' => [
                'id' => $partnerRide->id,
                'pickup_location' => $partnerRide->pickup_location,
                'pickup_latitude' => $partnerRide->pickup_latitude,
                'pickup_longitude' => $partnerRide->pickup_longitude,
                'destination' => $partnerRide->destination,
                'destination_latitude' => $partnerRide->destination_latitude,
                'destination_longitude' => $partnerRide->destination_longitude,
                'amount' => $partnerRide->amount,
                'status' => $partnerRide->status,
            ],
            'created_at' => $sharedRide->created_at,
        ];

        $notify[] = 'Shared ride details';
        return apiResponse("shared_ride_details", "success", $notify, [
            'shared_ride' => $sharedRideDetails,
            'user_image_path' => getFilePath('user'),
        ]);
    }

    /**
     * Cancel/opt out of a shared ride
     */
    public function cancelSharedRide(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'shared_ride_id' => 'required|exists:shared_rides,id',
            'cancel_reason' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return apiResponse("validation_error", "error", $validator->errors()->all());
        }

        $user = Auth::user();

        // Get the shared ride
        $sharedRide = SharedRide::with(['primaryRide.user', 'secondaryRide.user'])
            ->where(function ($query) use ($user) {
                $query->whereHas('primaryRide', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                })->orWhereHas('secondaryRide', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                });
            })
            ->where('status', SharedRide::STATUS_ACCEPTED)
            ->find($request->shared_ride_id);

        if (!$sharedRide) {
            $notify[] = 'Shared ride not found or not eligible for cancellation';
            return apiResponse("not_found", "error", $notify);
        }

        // Check if rides are still in a cancellable state
        $primaryRide = $sharedRide->primaryRide;
        $secondaryRide = $sharedRide->secondaryRide;

        if (!$primaryRide || !$secondaryRide) {
            $notify[] = 'Invalid shared ride data';
            return apiResponse("invalid_data", "error", $notify);
        }

        // Check if either ride has already started or completed
        if (in_array($primaryRide->status, [Status::RIDE_RUNNING, Status::RIDE_END, Status::RIDE_COMPLETED]) ||
            in_array($secondaryRide->status, [Status::RIDE_RUNNING, Status::RIDE_END, Status::RIDE_COMPLETED])) {
            $notify[] = 'Cannot cancel shared ride after it has started';
            return apiResponse("cannot_cancel", "error", $notify);
        }

        // Determine which user is canceling and get the other user
        $cancelingUser = $user;
        $otherUser = null;
        $cancelingRide = null;
        $otherRide = null;

        if ($primaryRide->user_id == $user->id) {
            $otherUser = $secondaryRide->user;
            $cancelingRide = $primaryRide;
            $otherRide = $secondaryRide;
        } else {
            $otherUser = $primaryRide->user;
            $cancelingRide = $secondaryRide;
            $otherRide = $primaryRide;
        }

        // Update shared ride status
        $sharedRide->status = SharedRide::STATUS_CANCELED;
        $sharedRide->save();

        // Update both rides to remove shared status
        $primaryRide->is_shared = false;
        $primaryRide->shared_ride_id = 0;
        $primaryRide->save();

        $secondaryRide->is_shared = false;
        $secondaryRide->shared_ride_id = 0;
        $secondaryRide->save();

        // Send notification to the other user
        $shortCodes = [
            'username' => $cancelingUser->username,
            'cancel_reason' => $request->cancel_reason,
            'ride_id' => $otherRide->uid,
        ];
        notify($otherUser, 'SHARED_RIDE_CANCELED', $shortCodes);

        // Send real-time Pusher notification
        initializePusher();
        $eventData = [
            'shared_ride_id' => $sharedRide->id,
            'canceling_user' => [
                'id' => $cancelingUser->id,
                'name' => $cancelingUser->fullname,
                'username' => $cancelingUser->username,
            ],
            'cancel_reason' => $request->cancel_reason,
            'message' => $cancelingUser->username . ' has opted out of the shared ride',
            'user_image_path' => getFilePath('user'),
        ];

        // Send notification to the other user
        $channelName = 'user-' . $otherUser->id;
        event(new NewRide($channelName, $eventData, 'shared-ride-canceled'));

        // Also emit events on both ride channels
        event(new EventsRide($primaryRide, 'shared-ride-canceled', $eventData));
        event(new EventsRide($secondaryRide, 'shared-ride-canceled', $eventData));

        $notify[] = 'You have successfully opted out of the shared ride';
        return apiResponse("shared_ride_canceled", "success", $notify, [
            'shared_ride_id' => $sharedRide->id,
            'your_ride' => $cancelingRide,
            'other_user' => [
                'id' => $otherUser->id,
                'name' => $otherUser->fullname,
                'username' => $otherUser->username,
            ]
        ]);
    }

    /**
     * Get active shared rides for the authenticated user
     */
    public function getActiveSharedRides()
    {
        $user = Auth::user();

        // Get active shared rides (excluding canceled ones)
        $sharedRides = SharedRide::with(['primaryRide.user', 'secondaryRide.user'])
            ->where(function ($query) use ($user) {
                $query->whereHas('primaryRide', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                })->orWhereHas('secondaryRide', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                });
            })
            ->whereIn('status', [SharedRide::STATUS_ACCEPTED, SharedRide::STATUS_PENDING])
            ->get();

        $activeSharedRides = [];

        foreach ($sharedRides as $sharedRide) {
            // Determine the partner's ride and user
            $userRide = null;
            $partnerRide = null;
            $partnerUser = null;

            if ($sharedRide->primaryRide->user_id == $user->id) {
                $userRide = $sharedRide->primaryRide;
                $partnerRide = $sharedRide->secondaryRide;
                $partnerUser = $sharedRide->secondaryRide->user;
            } else {
                $userRide = $sharedRide->secondaryRide;
                $partnerRide = $sharedRide->primaryRide;
                $partnerUser = $sharedRide->primaryRide->user;
            }

            $activeSharedRides[] = [
                'id' => $sharedRide->id,
                'status' => $sharedRide->status,
                'user_ride' => [
                    'id' => $userRide->id,
                    'pickup_location' => $userRide->pickup_location,
                    'destination' => $userRide->destination,
                    'status' => $userRide->status,
                ],
                'partner' => [
                    'id' => $partnerUser->id,
                    'name' => $partnerUser->fullname,
                    'username' => $partnerUser->username,
                    'image' => $partnerUser->image,
                ],
                'partner_ride' => [
                    'id' => $partnerRide->id,
                    'pickup_location' => $partnerRide->pickup_location,
                    'destination' => $partnerRide->destination,
                    'status' => $partnerRide->status,
                ],
                'created_at' => $sharedRide->created_at,
            ];
        }

        $notify[] = 'Active shared rides';
        return apiResponse("active_shared_rides", "success", $notify, [
            'shared_rides' => $activeSharedRides,
            'user_image_path' => getFilePath('user'),
        ]);
    }

    /**
     * Calculate distance between two points using Haversine formula
     */
    private function calculateDistance($lat1, $lon1, $lat2, $lon2)
    {
        $earthRadius = 6371; // Radius of the earth in km

        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);

        $a = sin($dLat/2) * sin($dLat/2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLon/2) * sin($dLon/2);
        $c = 2 * atan2(sqrt($a), sqrt(1-$a));
        $distance = $earthRadius * $c; // Distance in km

        return $distance;
    }
}
