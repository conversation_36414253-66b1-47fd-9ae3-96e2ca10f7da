# Vehicle Video Verification API Documentation

This document outlines the API endpoints for the vehicle video verification feature, which allows drivers to upload videos of their vehicles as part of the verification process.

## Overview

The vehicle video verification feature enhances the driver verification process by:
1. Allowing drivers to upload videos of their vehicles for verification
2. Enabling admins to visually verify vehicle information
3. Providing additional security and transparency in the verification process
4. Helping ensure vehicle authenticity and condition

## Database Changes

### Migration Required

Before using this feature, run the following migration:

```sql
ALTER TABLE `drivers` 
ADD COLUMN `vehicle_verification_video` VARCHAR(255) NULL AFTER `vehicle_data`,
ADD COLUMN `vehicle_verification_description` TEXT NULL AFTER `vehicle_verification_video`;
```

Or run the Laravel migration:
```bash
php artisan migrate
```

### New Fields Added to `drivers` Table

- `vehicle_verification_video` (VARCHAR 255, nullable) - Stores the filename of the uploaded video
- `vehicle_verification_description` (TEXT, nullable) - Stores optional description provided by the driver

## API Endpoints

### Driver Endpoints

#### 1. Upload Vehicle Verification Video

Allows drivers to upload a video of their vehicle for verification purposes.

**Endpoint:** `POST /api/driver/vehicle-video-verification`

**Authentication:** Required (Driver token)

**Headers:**
```
Authorization: Bearer {driver_token}
Content-Type: multipart/form-data
```

**Request Parameters:**
- `video` (required, file) - Video file of the vehicle
  - Supported formats: mp4, mov, 3gp, avi
  - Maximum file size: As configured in the system
- `description` (optional, string, max 255 chars) - Description of the video content

**Request Example:**
```bash
curl -X POST \
  https://yourdomain.com/api/driver/vehicle-video-verification \
  -H 'Authorization: Bearer {driver_token}' \
  -F 'video=@/path/to/vehicle_video.mp4' \
  -F 'description=Video showing my car from all angles including license plate'
```

**Success Response (200):**
```json
{
    "remark": "video_uploaded",
    "status": "success",
    "message": {
        "success": ["Vehicle verification video uploaded successfully"]
    },
    "data": {
        "video_filename": "64f8a1b2c3d4e5f67890abcd.mp4",
        "video_path": "assets/videos/driver",
        "description": "Video showing my car from all angles including license plate"
    }
}
```

**Error Responses:**

*Validation Error (422):*
```json
{
    "remark": "validation_error",
    "status": "error",
    "message": {
        "error": [
            "The video field is required.",
            "The video must be a file of type: mp4, mov, 3gp, avi."
        ]
    }
}
```

*Already Verified (400):*
```json
{
    "remark": "already_verified",
    "status": "error",
    "message": {
        "error": ["Your vehicle information is already verified"]
    }
}
```

*Upload Error (500):*
```json
{
    "remark": "exception",
    "status": "error",
    "message": {
        "error": ["Couldn't upload your video"]
    }
}
```

#### 2. Get Vehicle Verification Video

Retrieves the current vehicle verification video information for the authenticated driver.

**Endpoint:** `GET /api/driver/vehicle-video-verification`

**Authentication:** Required (Driver token)

**Headers:**
```
Authorization: Bearer {driver_token}
```

**Request Example:**
```bash
curl -X GET \
  https://yourdomain.com/api/driver/vehicle-video-verification \
  -H 'Authorization: Bearer {driver_token}'
```

**Success Response (200):**
```json
{
    "remark": "vehicle_video",
    "status": "success",
    "message": {
        "success": ["Vehicle verification video retrieved successfully"]
    },
    "data": {
        "video_filename": "64f8a1b2c3d4e5f67890abcd.mp4",
        "video_url": "assets/videos/driver/64f8a1b2c3d4e5f67890abcd.mp4",
        "video_path": "assets/videos/driver",
        "description": "Video showing my car from all angles including license plate",
        "verification_status": 2
    }
}
```

**Error Response (404):**
```json
{
    "remark": "no_video",
    "status": "error",
    "message": {
        "error": ["No vehicle verification video found"]
    }
}
```

## Verification Status Codes

The `verification_status` field in responses corresponds to the following values:
- `0` - Unverified
- `1` - Verified  
- `2` - Pending Review
- `9` - Rejected

## File Storage

- Videos are stored in the `assets/videos/driver/` directory
- File names are automatically generated using unique identifiers
- Old videos are automatically replaced when new ones are uploaded
- Supported video formats: MP4, MOV, 3GP, AVI

## Security Considerations

1. **Authentication Required:** All endpoints require valid driver authentication tokens
2. **File Type Validation:** Only specific video formats are allowed
3. **File Size Limits:** Configurable maximum file size limits apply
4. **Automatic Cleanup:** Old videos are removed when new ones are uploaded
5. **Admin Notifications:** Admins are notified when new videos are uploaded

## Integration Notes

1. **Admin Interface:** Admins can view uploaded videos through the admin panel at the vehicle verification section
2. **Notifications:** The system sends admin notifications when videos are uploaded
3. **Status Updates:** Vehicle verification status is automatically updated to "Pending" when videos are uploaded
4. **File Management:** The system handles file cleanup and storage management automatically

## Error Handling

The API uses standard HTTP status codes and returns detailed error messages in a consistent format. Common error scenarios include:

- Invalid file types
- Missing authentication
- File upload failures
- Already verified vehicles
- Missing video files

## Testing

To test the implementation:

1. Ensure the database migration has been run
2. Create a test driver account
3. Obtain a valid authentication token
4. Upload a test video using the upload endpoint
5. Retrieve the video information using the get endpoint
6. Verify admin notifications are created
7. Check file storage in the designated directory

## Support

For technical support or questions about this API, please contact the development team or refer to the main API documentation.
