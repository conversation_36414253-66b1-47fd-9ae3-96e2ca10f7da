@extends('admin.layouts.app')

@section('panel')
    <div class="row">
        <div class="col-lg-12">
            <div class="card b-radius--10">
                <div class="card-body p-0">
                    <div class="table-responsive--md table-responsive">
                        <table class="table--light style--two table">
                            <thead>
                                <tr>
                                    <th>@lang('Admin')</th>
                                    <th>@lang('Email')</th>
                                    <th>@lang('Role')</th>
                                    <th>@lang('Status')</th>
                                    <th>@lang('Last Login')</th>
                                    <th>@lang('Action')</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($admins as $admin)
                                    <tr>
                                        <td>
                                            <div class="user">
                                                <div class="thumb">
                                                    <img src="{{ $admin->imageSrc }}" alt="@lang('image')">
                                                </div>
                                                <span class="name">{{ $admin->name }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="fw-bold">{{ $admin->email }}</span><br>
                                            <span class="small">{{ '@' . $admin->username }}</span>
                                        </td>
                                        <td>
                                            @if($admin->role)
                                                {!! $admin->role->roleBadge !!}
                                            @else
                                                <span class="badge badge--secondary">@lang('No Role')</span>
                                            @endif
                                        </td>
                                        <td>
                                            {!! $admin->statusBadge !!}
                                        </td>
                                        <td>
                                            <span class="d-block">{{ showDateTime($admin->updated_at) }}</span>
                                            <span class="d-block">{{ diffForHumans($admin->updated_at) }}</span>
                                        </td>
                                        <td>
                                            <div class="button--group">
                                                <a href="{{ route('admin.staff.edit', $admin->id) }}" class="btn btn-sm btn-outline--primary">
                                                    <i class="la la-pencil"></i> @lang('Edit')
                                                </a>
                                                
                                                <a href="{{ route('admin.staff.permissions', $admin->id) }}" class="btn btn-sm btn-outline--info">
                                                    <i class="la la-key"></i> @lang('Permissions')
                                                </a>

                                                @if($admin->status)
                                                    <button class="btn btn-sm btn-outline--danger confirmationBtn" 
                                                            data-action="{{ route('admin.staff.status', $admin->id) }}" 
                                                            data-question="@lang('Are you sure to disable this admin?')">
                                                        <i class="la la-eye-slash"></i> @lang('Disable')
                                                    </button>
                                                @else
                                                    <button class="btn btn-sm btn-outline--success confirmationBtn" 
                                                            data-action="{{ route('admin.staff.status', $admin->id) }}" 
                                                            data-question="@lang('Are you sure to enable this admin?')">
                                                        <i class="la la-eye"></i> @lang('Enable')
                                                    </button>
                                                @endif

                                                <button class="btn btn-sm btn-outline--danger confirmationBtn"
                                                        data-action="{{ route('admin.staff.delete', $admin->id) }}"
                                                        data-question="@lang('Are you sure to delete this admin?')"
                                                        data-method="DELETE">
                                                    <i class="la la-trash"></i> @lang('Delete')
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td class="text-muted text-center" colspan="100%">{{ __($emptyMessage) }}</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
                @if ($admins->hasPages())
                    <div class="card-footer py-4">
                        {{ paginateLinks($admins) }}
                    </div>
                @endif
            </div>
        </div>
    </div>

    <x-confirmation-modal />
@endsection

@push('breadcrumb-plugins')
    <a href="{{ route('admin.staff.create') }}" class="btn btn-sm btn--primary">
        <i class="la la-plus"></i>@lang('Add New Admin')
    </a>
    <a href="{{ route('admin.roles.index') }}" class="btn btn-sm btn--secondary">
        <i class="la la-users-cog"></i>@lang('Manage Roles')
    </a>
@endpush
