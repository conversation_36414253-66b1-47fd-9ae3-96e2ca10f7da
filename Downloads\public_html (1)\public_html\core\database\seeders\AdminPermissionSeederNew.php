<?php

namespace Database\Seeders;

use App\Models\AdminPermission;
use Illuminate\Database\Seeder;

class AdminPermissionSeederNew extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        $permissions = [
            // Main
            [
                'name' => 'Dashboard',
                'slug' => 'dashboard',
                'category' => 'main',
                'description' => 'Access admin dashboard'
            ],
            [
                'name' => 'Manage Rides',
                'slug' => 'manage_rides',
                'category' => 'main',
                'description' => 'View and manage all rides'
            ],
            [
                'name' => 'System Setup',
                'slug' => 'system_setup',
                'category' => 'main',
                'description' => 'Access system setup features'
            ],
            [
                'name' => 'All Reviews',
                'slug' => 'all_reviews',
                'category' => 'main',
                'description' => 'View and manage reviews'
            ],
            [
                'name' => 'Promotional Notify',
                'slug' => 'promotional_notify',
                'category' => 'main',
                'description' => 'Send promotional notifications'
            ],

            // People
            [
                'name' => 'Manage Riders',
                'slug' => 'manage_riders',
                'category' => 'people',
                'description' => 'View and manage riders'
            ],
            [
                'name' => 'Manage Drivers',
                'slug' => 'manage_drivers',
                'category' => 'people',
                'description' => 'View and manage drivers'
            ],

            // Finance
            [
                'name' => 'Driver Deposits',
                'slug' => 'driver_deposits',
                'category' => 'finance',
                'description' => 'Manage driver deposits'
            ],
            [
                'name' => 'Driver Withdrawals',
                'slug' => 'driver_withdrawals',
                'category' => 'finance',
                'description' => 'Manage driver withdrawals'
            ],
            [
                'name' => 'Gateways',
                'slug' => 'gateways',
                'category' => 'finance',
                'description' => 'Manage payment gateways'
            ],

            // Verification
            [
                'name' => 'Driver Verification Form',
                'slug' => 'driver_verification_form',
                'category' => 'verification',
                'description' => 'Manage driver verification forms'
            ],
            [
                'name' => 'Vehicle Verification Form',
                'slug' => 'vehicle_verification_form',
                'category' => 'verification',
                'description' => 'Manage vehicle verification forms'
            ],

            // System Report
            [
                'name' => 'Rider Report',
                'slug' => 'rider_report',
                'category' => 'system_report',
                'description' => 'View rider reports and analytics'
            ],
            [
                'name' => 'Driver Report',
                'slug' => 'driver_report',
                'category' => 'system_report',
                'description' => 'View driver reports and analytics'
            ],

            // System Utilities
            [
                'name' => 'Manage Extension',
                'slug' => 'manage_extension',
                'category' => 'system_utilities',
                'description' => 'Manage system extensions'
            ],
            [
                'name' => 'Manage SEO',
                'slug' => 'manage_seo',
                'category' => 'system_utilities',
                'description' => 'Manage SEO settings'
            ],
            [
                'name' => 'Manage Language',
                'slug' => 'manage_language',
                'category' => 'system_utilities',
                'description' => 'Manage application languages'
            ],

            // Admin Management
            [
                'name' => 'Manage Admins',
                'slug' => 'manage_admins',
                'category' => 'admin_management',
                'description' => 'Manage admin users'
            ],
            [
                'name' => 'Manage Roles',
                'slug' => 'manage_roles',
                'category' => 'admin_management',
                'description' => 'Manage admin roles and permissions'
            ],

            // Settings
            [
                'name' => 'General Settings',
                'slug' => 'general_settings',
                'category' => 'settings',
                'description' => 'Manage general application settings'
            ],
            [
                'name' => 'Brand Setting',
                'slug' => 'brand_setting',
                'category' => 'settings',
                'description' => 'Manage brand and logo settings'
            ],
            [
                'name' => 'System Configuration',
                'slug' => 'system_configuration',
                'category' => 'settings',
                'description' => 'Manage system configuration'
            ],
            [
                'name' => 'Notification Setting',
                'slug' => 'notification_setting',
                'category' => 'settings',
                'description' => 'Manage notification settings'
            ],
            [
                'name' => 'CRON Job Setting',
                'slug' => 'cron_job_setting',
                'category' => 'settings',
                'description' => 'Manage CRON job settings'
            ],
            [
                'name' => 'GDPR Cookie',
                'slug' => 'gdpr_cookie',
                'category' => 'settings',
                'description' => 'Manage GDPR cookie settings'
            ],
            [
                'name' => 'Custom CSS',
                'slug' => 'custom_css',
                'category' => 'settings',
                'description' => 'Manage custom CSS'
            ],
            [
                'name' => 'Sitemap XML',
                'slug' => 'sitemap_xml',
                'category' => 'settings',
                'description' => 'Manage sitemap XML'
            ],
            [
                'name' => 'Robots txt',
                'slug' => 'robots_txt',
                'category' => 'settings',
                'description' => 'Manage robots.txt'
            ],
            [
                'name' => 'Maintenance Mode',
                'slug' => 'maintenance_mode',
                'category' => 'settings',
                'description' => 'Control maintenance mode'
            ],

            // Frontend Manager
            [
                'name' => 'Manage Pages',
                'slug' => 'manage_pages',
                'category' => 'frontend_manager',
                'description' => 'Manage frontend pages'
            ],
            [
                'name' => 'Manage Sections',
                'slug' => 'manage_sections',
                'category' => 'frontend_manager',
                'description' => 'Manage frontend sections'
            ],

            // Other
            [
                'name' => 'Support Ticket',
                'slug' => 'support_ticket',
                'category' => 'other',
                'description' => 'Manage support tickets'
            ],
            [
                'name' => 'Manage Subscriber',
                'slug' => 'manage_subscriber',
                'category' => 'other',
                'description' => 'Manage newsletter subscribers'
            ],
            [
                'name' => 'Application Information',
                'slug' => 'application_information',
                'category' => 'other',
                'description' => 'View application information'
            ],
        ];

        foreach ($permissions as $permission) {
            AdminPermission::updateOrCreate(
                ['slug' => $permission['slug']],
                $permission
            );
        }
    }

    /**
     * Get permission categories
     */
    public static function getCategories()
    {
        return [
            'main' => 'Main',
            'people' => 'People',
            'finance' => 'Finance',
            'verification' => 'Verification',
            'system_report' => 'System Report',
            'system_utilities' => 'System Utilities',
            'admin_management' => 'Admin Management',
            'settings' => 'Settings',
            'frontend_manager' => 'Frontend Manager',
            'other' => 'Other',
        ];
    }
}
