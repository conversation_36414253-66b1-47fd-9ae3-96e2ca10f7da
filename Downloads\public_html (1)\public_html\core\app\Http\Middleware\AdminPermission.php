<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AdminPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @param  string  $permission
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next, $permission = null)
    {
        $admin = Auth::guard('admin')->user();

        if (!$admin) {
            return redirect()->route('admin.login');
        }

        // Check if admin is active (only if status column exists)
        if (isset($admin->status) && !$admin->status) {
            Auth::guard('admin')->logout();
            $notify[] = ['error', 'Your account has been deactivated. Please contact the administrator.'];
            return redirect()->route('admin.login')->withNotify($notify);
        }

        // Super admin has access to everything (if method exists)
        if (method_exists($admin, 'isSuperAdmin') && $admin->isSuperAdmin()) {
            return $next($request);
        }

        // If specific permission is required, check it (if method exists)
        if ($permission && method_exists($admin, 'hasPermission') && !$admin->hasPermission($permission)) {
            $notify[] = ['error', 'You do not have permission to access this resource.'];

            // Try to redirect to dashboard, fallback to login if dashboard doesn't exist
            try {
                return redirect()->route('admin.dashboard')->withNotify($notify);
            } catch (\Exception $e) {
                return redirect()->route('admin.login')->withNotify($notify);
            }
        }

        // If no specific permission, check route-based permissions (if method exists)
        if (!$permission && method_exists($admin, 'canAccess')) {
            $routeName = $request->route()->getName();
            if (!$admin->canAccess($routeName)) {
                $notify[] = ['error', 'You do not have permission to access this resource.'];

                // Try to redirect to dashboard, fallback to login if dashboard doesn't exist
                try {
                    return redirect()->route('admin.dashboard')->withNotify($notify);
                } catch (\Exception $e) {
                    return redirect()->route('admin.login')->withNotify($notify);
                }
            }
        }

        return $next($request);
    }
}
