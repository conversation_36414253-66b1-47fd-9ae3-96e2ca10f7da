<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AdminRole;
use App\Models\AdminPermission;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class RoleManagementController extends Controller
{
    public function index()
    {
        try {
            $pageTitle = 'Role Management';
            $roles = AdminRole::withCount(['admins', 'permissions'])->latest()->paginate(getPaginate());
            return view('admin.roles.index', compact('pageTitle', 'roles'));
        } catch (\Exception $e) {
            $notify[] = ['error', 'Error loading roles: ' . $e->getMessage()];
            return redirect()->route('admin.dashboard')->withNotify($notify);
        }
    }

    public function create()
    {
        try {
            $pageTitle = 'Create Role';
            $permissions = AdminPermission::active()->get()->groupBy('category');
            $categories = AdminPermission::getCategories();
            return view('admin.roles.create', compact('pageTitle', 'permissions', 'categories'));
        } catch (\Exception $e) {
            $notify[] = ['error', 'Error loading create role page: ' . $e->getMessage()];
            return redirect()->route('admin.dashboard')->withNotify($notify);
        }
    }

    public function store(Request $request)
    {
        try {
            $request->validate([
                'name' => 'required|string|max:100|unique:admin_roles,name',
                'description' => 'nullable|string',
                'permissions' => 'required|array',
                'permissions.*' => 'exists:admin_permissions,id',
                'status' => 'required|boolean'
            ]);

            $role = new AdminRole();
            $role->name = $request->name;
            $role->slug = Str::slug($request->name);
            $role->description = $request->description;
            $role->status = $request->status;
            $role->save();

            // Sync permissions
            if ($request->has('permissions')) {
                $role->permissions()->sync($request->permissions);
            }

            $notify[] = ['success', 'Role created successfully'];
            return redirect()->route('admin.roles.index')->withNotify($notify);
        } catch (\Exception $e) {
            $notify[] = ['error', 'Error creating role: ' . $e->getMessage()];
            return back()->withNotify($notify)->withInput();
        }
    }

    public function edit($id)
    {
        $pageTitle = 'Edit Role';
        $role = AdminRole::with('permissions')->findOrFail($id);
        $permissions = AdminPermission::active()->get()->groupBy('category');
        $categories = AdminPermission::getCategories();
        $rolePermissions = $role->permissions->pluck('id')->toArray();
        
        return view('admin.roles.edit', compact('pageTitle', 'role', 'permissions', 'categories', 'rolePermissions'));
    }

    public function update(Request $request, $id)
    {
        $role = AdminRole::findOrFail($id);

        $request->validate([
            'name' => ['required', 'string', 'max:100', Rule::unique('admin_roles')->ignore($role->id)],
            'description' => 'nullable|string',
            'permissions' => 'required|array',
            'permissions.*' => 'exists:admin_permissions,id',
            'status' => 'required|boolean'
        ]);

        $role->name = $request->name;
        $role->slug = Str::slug($request->name);
        $role->description = $request->description;
        $role->status = $request->status;
        $role->save();

        // Sync permissions
        $role->permissions()->sync($request->permissions);

        $notify[] = ['success', 'Role updated successfully'];
        return redirect()->route('admin.roles.index')->withNotify($notify);
    }

    public function destroy($id)
    {
        $role = AdminRole::findOrFail($id);
        
        // Check if role has admins
        if ($role->admins()->count() > 0) {
            $notify[] = ['error', 'Cannot delete role that has assigned admins'];
            return back()->withNotify($notify);
        }

        $role->delete();

        $notify[] = ['success', 'Role deleted successfully'];
        return back()->withNotify($notify);
    }

    public function status($id)
    {
        $role = AdminRole::findOrFail($id);
        $role->status = !$role->status;
        $role->save();

        $notify[] = ['success', 'Role status updated successfully'];
        return back()->withNotify($notify);
    }

    public function permissions($id)
    {
        $pageTitle = 'Role Permissions';
        $role = AdminRole::with('permissions')->findOrFail($id);
        $permissions = AdminPermission::active()->get()->groupBy('category');
        $categories = AdminPermission::getCategories();
        $rolePermissions = $role->permissions->pluck('id')->toArray();
        
        return view('admin.roles.permissions', compact('pageTitle', 'role', 'permissions', 'categories', 'rolePermissions'));
    }

    public function updatePermissions(Request $request, $id)
    {
        $role = AdminRole::findOrFail($id);

        $request->validate([
            'permissions' => 'required|array',
            'permissions.*' => 'exists:admin_permissions,id'
        ]);

        // Sync permissions
        $role->permissions()->sync($request->permissions);

        $notify[] = ['success', 'Role permissions updated successfully'];
        return back()->withNotify($notify);
    }
}
