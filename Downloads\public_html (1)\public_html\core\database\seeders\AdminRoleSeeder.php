<?php

namespace Database\Seeders;

use App\Models\Admin;
use App\Models\AdminRole;
use App\Models\AdminPermission;
use Illuminate\Database\Seeder;

class AdminRoleSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Create default roles
        $roles = [
            [
                'name' => 'Driver Manager',
                'slug' => 'driver-manager',
                'description' => 'Manages all driver-related operations including verification and vehicle management',
                'permissions' => ['driver_management', 'driver_verification', 'vehicle_verification']
            ],
            [
                'name' => 'Rider Manager',
                'slug' => 'rider-manager',
                'description' => 'Manages all rider-related operations and verification',
                'permissions' => ['rider_management', 'rider_verification']
            ],
            [
                'name' => 'Finance Manager',
                'slug' => 'finance-manager',
                'description' => 'Manages all financial operations including deposits and withdrawals',
                'permissions' => ['payment_management', 'deposit_management', 'withdrawal_management', 'financial_reports']
            ],
            [
                'name' => 'Operations Manager',
                'slug' => 'operations-manager',
                'description' => 'Manages rides and general operations',
                'permissions' => ['ride_management', 'ride_analytics', 'reports_analytics']
            ],
            [
                'name' => 'Content Manager',
                'slug' => 'content-manager',
                'description' => 'Manages general settings and content',
                'permissions' => ['general_settings', 'language_management', 'seo_settings']
            ],
            [
                'name' => 'System Administrator',
                'slug' => 'system-administrator',
                'description' => 'Manages system configuration and technical settings',
                'permissions' => ['system_configuration', 'maintenance_mode', 'cron_jobs']
            ]
        ];

        foreach ($roles as $roleData) {
            $role = AdminRole::updateOrCreate(
                ['slug' => $roleData['slug']],
                [
                    'name' => $roleData['name'],
                    'description' => $roleData['description'],
                    'status' => true
                ]
            );

            // Assign permissions to role
            $permissions = AdminPermission::whereIn('slug', $roleData['permissions'])->get();
            $role->permissions()->sync($permissions->pluck('id')->toArray());
        }

        // Update first admin to be super admin
        $firstAdmin = Admin::first();
        if ($firstAdmin) {
            $firstAdmin->update([
                'is_super_admin' => true,
                'status' => true
            ]);
        }

        // Create a sample staff admin for testing
        $staffAdmin = Admin::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Staff Admin',
                'username' => 'staff_admin',
                'email' => '<EMAIL>',
                'password' => bcrypt('password123'),
                'role_id' => AdminRole::where('slug', 'driver-manager')->first()?->id,
                'is_super_admin' => false,
                'status' => true
            ]
        );
    }
}
