# Fix Admin 401 Authentication Error

## Problem
Getting 401 errors and "File not found [/home/<USER>/public_html/401.shtml]" when trying to login as admin.

## Root Cause
The updated Admin model is trying to access database columns (`role_id`, `is_super_admin`, `status`) that don't exist yet, causing authentication to fail.

## Solutions (Try in Order)

### Solution 1: Quick Database Fix (Recommended)

**Step 1:** Run the SQL script to add missing columns
```sql
-- Copy and run the contents of docs/fix_admin_authentication.sql in your database
-- This will safely add the missing columns and make the first admin a super admin
```

**Step 2:** Clear Laravel cache
```bash
cd public_html/core
php artisan config:clear
php artisan route:clear
php artisan view:clear
```

### Solution 2: Test and Diagnose

**Step 1:** Run the diagnostic script
1. Upload `docs/test_admin_auth.php` to `public_html/core/`
2. Visit: `https://mride.org/test_admin_auth.php`
3. This will show you exactly what's wrong

**Step 2:** Based on the results, apply the appropriate fix

### Solution 3: Manual Database Update

If you have database access (phpMyAdmin, etc.):

```sql
-- Add missing columns to admins table
ALTER TABLE admins ADD COLUMN role_id bigint(20) UNSIGNED NULL AFTER email;
ALTER TABLE admins ADD COLUMN is_super_admin tinyint(1) NOT NULL DEFAULT 0 AFTER role_id;
ALTER TABLE admins ADD COLUMN status tinyint(1) NOT NULL DEFAULT 1 AFTER is_super_admin;

-- Make first admin super admin
UPDATE admins SET is_super_admin = 1, status = 1 WHERE id = 1;
```

### Solution 4: Temporary Rollback

If the above doesn't work, temporarily revert the Admin model:

**Step 1:** Backup current Admin.php
```bash
cp app/Models/Admin.php app/Models/Admin.php.backup
```

**Step 2:** Create a minimal Admin model:
```php
<?php
namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Admin extends Authenticatable
{
    protected $hidden = [
        'password',
        'remember_token',
    ];

    public function imageSrc(): Attribute
    {
        return new Attribute(
            get: fn() => $this->image ? getImage(getFilePath('admin') . '/' . $this->image, getFileSize('admin')) : siteFavicon(),
        );
    }
}
```

**Step 3:** Test admin login
**Step 4:** Once login works, add the database columns and restore the full model

## Verification Steps

### 1. Check Database Structure
```sql
DESCRIBE admins;
```
Should show columns: id, name, email, username, password, image, role_id, is_super_admin, status

### 2. Check Admin Data
```sql
SELECT id, name, email, is_super_admin, status FROM admins;
```
First admin should have is_super_admin = 1, status = 1

### 3. Test Login
Try logging into admin panel at: `https://mride.org/admin/login`

### 4. Test Role Management
Once logged in, try accessing: `https://mride.org/admin/debug-admin-roles`

## Common Issues and Fixes

### Issue: "Column 'role_id' doesn't exist"
**Fix:** Run the SQL script to add missing columns

### Issue: "Class 'App\Models\AdminRole' not found"
**Fix:** Ensure all model files are uploaded:
- `app/Models/AdminRole.php`
- `app/Models/AdminPermission.php`

### Issue: "Route [admin.roles.index] not defined"
**Fix:** Clear route cache: `php artisan route:clear`

### Issue: Still getting 401 errors
**Fix:** Check .env database credentials and test database connection

## Prevention

To prevent this in the future:
1. Always run migrations before updating models
2. Test in development environment first
3. Use feature flags for gradual rollouts
4. Keep backups of working code

## Emergency Rollback

If nothing works, restore the original Admin model:
1. Remove the role-related code from Admin.php
2. Remove the AdminPermission middleware from routes
3. Comment out the admin management routes
4. Clear all caches

## Files Modified for Compatibility

The following files have been updated to handle missing database columns gracefully:

1. **app/Models/Admin.php** - Added null checks for new columns
2. **app/Http/Middleware/AdminPermission.php** - Added method existence checks
3. **routes/admin.php** - Added debug route

## Next Steps After Fix

Once admin authentication is working:

1. **Add the role system tables:**
   ```bash
   php artisan migrate
   php artisan db:seed --class=AdminPermissionSeeder
   php artisan db:seed --class=AdminRoleSeeder
   ```

2. **Test role creation:**
   - Go to Admin Management → Manage Roles
   - Try creating a new role

3. **Create staff admins:**
   - Go to Admin Management → Manage Admins
   - Create new admins with specific roles

## Support

If you're still having issues:

1. Run the diagnostic script and share the output
2. Check Laravel logs: `storage/logs/laravel.log`
3. Verify PHP version and extensions
4. Test database connection independently

The key is to get basic admin authentication working first, then gradually add the role system features.
