<?php

namespace App\Http\Controllers\Admin;

use App\Constants\Status;
use App\Http\Controllers\Controller;
use App\Models\Admin;
use App\Models\AdminRole;
use App\Rules\FileTypeValidate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Validation\Rule;

class AdminManagementController extends Controller
{
    public function index()
    {
        try {
            $pageTitle = 'Admin Management';

            // Check if role relationship exists before eager loading
            $query = Admin::staff()->latest();

            // Only eager load role if the relationship method exists and tables exist
            try {
                // Test if we can access the role relationship
                $testAdmin = Admin::first();
                if ($testAdmin && method_exists($testAdmin, 'role')) {
                    $testRelation = $testAdmin->role();
                    if ($testRelation !== null) {
                        $query = $query->with('role');
                    }
                }
            } catch (\Exception $relationError) {
                // Role relationship doesn't work, continue without it
            }

            // Get pagination count with fallback
            $paginateCount = function_exists('getPaginate') ? getPaginate() : 20;
            $admins = $query->paginate($paginateCount);
            return view('admin.staff.index', compact('pageTitle', 'admins'));
        } catch (\Exception $e) {
            $notify[] = ['error', 'Error loading admin list: ' . $e->getMessage()];
            return redirect()->route('admin.dashboard')->withNotify($notify);
        }
    }

    public function create()
    {
        try {
            $pageTitle = 'Add New Admin';

            // Try to get roles, fallback to empty collection if tables don't exist
            try {
                $roles = AdminRole::active()->get();
            } catch (\Exception $e) {
                $roles = collect(); // Empty collection
            }

            return view('admin.staff.create', compact('pageTitle', 'roles'));
        } catch (\Exception $e) {
            $notify[] = ['error', 'Error loading create admin page: ' . $e->getMessage()];
            return redirect()->route('admin.staff.index')->withNotify($notify);
        }
    }

    public function store(Request $request)
    {
        try {
            // Build validation rules dynamically based on what exists
            $rules = [
                'name' => 'required|string|max:255',
                'username' => 'required|string|max:255|unique:admins,username',
                'email' => 'required|email|max:255|unique:admins,email',
                'password' => 'required|string|min:6|confirmed',
                'image' => ['nullable', 'image', new FileTypeValidate(['jpg', 'jpeg', 'png'])],
                'status' => 'required|boolean'
            ];

            // Only add role validation if admin_roles table exists
            try {
                DB::table('admin_roles')->count();
                $rules['role_id'] = 'required|exists:admin_roles,id';
            } catch (\Exception $e) {
                // admin_roles table doesn't exist, skip role validation
            }

            $request->validate($rules);

            $admin = new Admin();
            $admin->name = $request->name;
            $admin->username = $request->username;
            $admin->email = $request->email;
            $admin->password = Hash::make($request->password);

            // Only set role_id if the field exists and was validated
            if (isset($rules['role_id']) && $request->has('role_id')) {
                $admin->role_id = $request->role_id;
            }

            // Only set status if the column exists
            if (Schema::hasColumn('admins', 'status')) {
                $admin->status = $request->status;
            }

            // Only set is_super_admin if the column exists
            if (Schema::hasColumn('admins', 'is_super_admin')) {
                $admin->is_super_admin = false;
            }

            if ($request->hasFile('image')) {
                try {
                    $admin->image = fileUploader($request->image, getFilePath('admin'), getFileSize('admin'));
                } catch (\Exception $exp) {
                    $notify[] = ['error', 'Couldn\'t upload image'];
                    return back()->withNotify($notify);
                }
            }

            $admin->save();

            $notify[] = ['success', 'Admin created successfully'];
            return redirect()->route('admin.staff.index')->withNotify($notify);
        } catch (\Exception $e) {
            $notify[] = ['error', 'Error creating admin: ' . $e->getMessage()];
            return back()->withNotify($notify)->withInput();
        }
    }

    public function edit($id)
    {
        try {
            $pageTitle = 'Edit Admin';
            $admin = Admin::staff()->findOrFail($id);

            // Try to get roles, fallback to empty collection if tables don't exist
            try {
                $roles = AdminRole::active()->get();
            } catch (\Exception $e) {
                $roles = collect(); // Empty collection
            }

            return view('admin.staff.edit', compact('pageTitle', 'admin', 'roles'));
        } catch (\Exception $e) {
            $notify[] = ['error', 'Error loading admin edit page: ' . $e->getMessage()];
            return redirect()->route('admin.staff.index')->withNotify($notify);
        }
    }

    public function update(Request $request, $id)
    {
        try {
            $admin = Admin::staff()->findOrFail($id);

            // Build validation rules dynamically based on what exists
            $rules = [
                'name' => 'required|string|max:255',
                'username' => ['required', 'string', 'max:255', Rule::unique('admins')->ignore($admin->id)],
                'email' => ['required', 'email', 'max:255', Rule::unique('admins')->ignore($admin->id)],
                'password' => 'nullable|string|min:6|confirmed',
                'image' => ['nullable', 'image', new FileTypeValidate(['jpg', 'jpeg', 'png'])],
                'status' => 'required|boolean'
            ];

            // Only add role validation if admin_roles table exists
            try {
                DB::table('admin_roles')->count();
                $rules['role_id'] = 'required|exists:admin_roles,id';
            } catch (\Exception $e) {
                // admin_roles table doesn't exist, skip role validation
            }

            $request->validate($rules);

            $admin->name = $request->name;
            $admin->username = $request->username;
            $admin->email = $request->email;

            // Only set role_id if the field exists and was validated
            if (isset($rules['role_id']) && $request->has('role_id')) {
                $admin->role_id = $request->role_id;
            }

            // Only set status if the column exists
            if (isset($admin->status) || Schema::hasColumn('admins', 'status')) {
                $admin->status = $request->status;
            }

            if ($request->filled('password')) {
                $admin->password = Hash::make($request->password);
            }

            if ($request->hasFile('image')) {
                try {
                    $admin->image = fileUploader($request->image, getFilePath('admin'), getFileSize('admin'), $admin->image);
                } catch (\Exception $exp) {
                    $notify[] = ['error', 'Couldn\'t upload image'];
                    return back()->withNotify($notify);
                }
            }

            $admin->save();

            $notify[] = ['success', 'Admin updated successfully'];
            return redirect()->route('admin.staff.index')->withNotify($notify);
        } catch (\Exception $e) {
            $notify[] = ['error', 'Error updating admin: ' . $e->getMessage()];
            return back()->withNotify($notify)->withInput();
        }
    }

    public function destroy($id)
    {
        $admin = Admin::staff()->findOrFail($id);
        $admin->delete();

        $notify[] = ['success', 'Admin deleted successfully'];
        return back()->withNotify($notify);
    }

    public function status($id)
    {
        $admin = Admin::staff()->findOrFail($id);
        $admin->status = !$admin->status;
        $admin->save();

        $notify[] = ['success', 'Admin status updated successfully'];
        return back()->withNotify($notify);
    }

    public function permissions($id)
    {
        try {
            $pageTitle = 'Admin Permissions';

            // Try to load admin with relationships, fallback to basic admin if relationships don't exist
            try {
                $admin = Admin::with('role.permissions')->staff()->findOrFail($id);
            } catch (\Exception $e) {
                // If relationships fail, just get the basic admin
                $admin = Admin::staff()->findOrFail($id);
                $admin->role = null;
            }

            return view('admin.staff.permissions', compact('pageTitle', 'admin'));
        } catch (\Exception $e) {
            $notify[] = ['error', 'Error loading admin permissions: ' . $e->getMessage()];
            return redirect()->route('admin.staff.index')->withNotify($notify);
        }
    }
}
