<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add notification template for shared ride cancellation
        DB::table('notification_templates')->insert([
            'act' => 'SHARED_RIDE_CANCELED',
            'name' => 'Shared Ride Canceled',
            'subject' => 'Your Shared Ride Has Been Canceled',
            'push_title' => 'Shared Ride Canceled',
            'email_body' => '<p>Hello {{fullname}},</p><p>Your shared ride partner {{username}} has opted out of the shared ride.</p><p>Ride ID: {{ride_id}}</p><p>Reason: {{cancel_reason}}</p><p>Your ride is now individual and you can continue to find a driver or look for another ride partner.</p><p>Thank you for using our service.</p><p>{{site_name}}</p>',
            'sms_body' => 'Hello {{fullname}}, Your shared ride partner {{username}} has opted out of the shared ride (ID: {{ride_id}}). Reason: {{cancel_reason}}',
            'push_body' => '{{username}} has opted out of your shared ride. Reason: {{cancel_reason}}',
            'shortcodes' => '{"username":"Canceling User Username","cancel_reason":"Cancellation Reason","ride_id":"Ride ID","fullname":"Recipient Fullname"}',
            'email_status' => 1,
            'sms_status' => 1,
            'push_status' => 1,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove notification template for shared ride cancellation
        DB::table('notification_templates')->where('act', 'SHARED_RIDE_CANCELED')->delete();
    }
};
