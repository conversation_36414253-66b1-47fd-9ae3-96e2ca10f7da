<?php

namespace Database\Seeders;

use App\Models\AdminPermission;
use Illuminate\Database\Seeder;

class AdminPermissionSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        $permissions = [
            // Driver Management
            [
                'name' => 'View Drivers',
                'slug' => 'driver_management',
                'category' => 'driver_management',
                'description' => 'View and manage drivers'
            ],
            [
                'name' => 'Driver Verification',
                'slug' => 'driver_verification',
                'category' => 'driver_management',
                'description' => 'Approve/reject driver verification'
            ],
            [
                'name' => 'Vehicle Verification',
                'slug' => 'vehicle_verification',
                'category' => 'driver_management',
                'description' => 'Approve/reject vehicle verification'
            ],

            // Rider Management
            [
                'name' => 'View Riders',
                'slug' => 'rider_management',
                'category' => 'rider_management',
                'description' => 'View and manage riders'
            ],
            [
                'name' => 'Rider Verification',
                'slug' => 'rider_verification',
                'category' => 'rider_management',
                'description' => 'Manage rider verification'
            ],

            // Ride Management
            [
                'name' => 'View Rides',
                'slug' => 'ride_management',
                'category' => 'ride_management',
                'description' => 'View and manage rides'
            ],
            [
                'name' => 'Ride Analytics',
                'slug' => 'ride_analytics',
                'category' => 'ride_management',
                'description' => 'View ride analytics and reports'
            ],

            // Payment Management
            [
                'name' => 'View Payments',
                'slug' => 'payment_management',
                'category' => 'payment_management',
                'description' => 'View payment transactions'
            ],
            [
                'name' => 'Manage Deposits',
                'slug' => 'deposit_management',
                'category' => 'payment_management',
                'description' => 'Manage user deposits'
            ],
            [
                'name' => 'Manage Withdrawals',
                'slug' => 'withdrawal_management',
                'category' => 'payment_management',
                'description' => 'Manage withdrawal requests'
            ],

            // General Settings
            [
                'name' => 'General Settings',
                'slug' => 'general_settings',
                'category' => 'general_settings',
                'description' => 'Manage general application settings'
            ],
            [
                'name' => 'Language Management',
                'slug' => 'language_management',
                'category' => 'general_settings',
                'description' => 'Manage application languages'
            ],
            [
                'name' => 'SEO Settings',
                'slug' => 'seo_settings',
                'category' => 'general_settings',
                'description' => 'Manage SEO settings'
            ],

            // Reports & Analytics
            [
                'name' => 'View Reports',
                'slug' => 'reports_analytics',
                'category' => 'reports_analytics',
                'description' => 'View system reports and analytics'
            ],
            [
                'name' => 'Financial Reports',
                'slug' => 'financial_reports',
                'category' => 'reports_analytics',
                'description' => 'View financial reports'
            ],

            // System Configuration
            [
                'name' => 'System Configuration',
                'slug' => 'system_configuration',
                'category' => 'system_configuration',
                'description' => 'Manage system configuration'
            ],
            [
                'name' => 'Maintenance Mode',
                'slug' => 'maintenance_mode',
                'category' => 'system_configuration',
                'description' => 'Control maintenance mode'
            ],
            [
                'name' => 'Cron Jobs',
                'slug' => 'cron_jobs',
                'category' => 'system_configuration',
                'description' => 'Manage cron jobs'
            ],

            // Admin Management
            [
                'name' => 'Admin Management',
                'slug' => 'admin_management',
                'category' => 'admin_management',
                'description' => 'Manage admin users'
            ],
            [
                'name' => 'Role Management',
                'slug' => 'role_management',
                'category' => 'admin_management',
                'description' => 'Manage admin roles and permissions'
            ],
        ];

        foreach ($permissions as $permission) {
            AdminPermission::updateOrCreate(
                ['slug' => $permission['slug']],
                $permission
            );
        }
    }
}
