@extends('admin.layouts.app')

@section('panel')
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">@lang('Role'): {{ $role->name }}</h5>
                    <p class="text-muted mb-0">{{ $role->description }}</p>
                </div>
                <form action="{{ route('admin.roles.permissions.update', $role->id) }}" method="POST">
                    @csrf
                    @method('PUT')
                    <div class="card-body">
                        @foreach($categories as $categoryKey => $categoryName)
                            @if(isset($permissions[$categoryKey]))
                                <div class="permission-category mb-4">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="mb-0">{{ $categoryName }}</h6>
                                        <div class="form-check">
                                            <input class="form-check-input category-toggle" type="checkbox" 
                                                   id="category_{{ $categoryKey }}" 
                                                   data-category="{{ $categoryKey }}">
                                            <label class="form-check-label" for="category_{{ $categoryKey }}">
                                                @lang('Select All')
                                            </label>
                                        </div>
                                    </div>
                                    <div class="row">
                                        @foreach($permissions[$categoryKey] as $permission)
                                            <div class="col-md-6 col-lg-4">
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input permission-checkbox" 
                                                           type="checkbox" 
                                                           name="permissions[]" 
                                                           value="{{ $permission->id }}" 
                                                           id="permission_{{ $permission->id }}"
                                                           data-category="{{ $categoryKey }}"
                                                           {{ in_array($permission->id, $rolePermissions) ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="permission_{{ $permission->id }}">
                                                        <strong>{{ $permission->name }}</strong>
                                                        @if($permission->description)
                                                            <br><small class="text-muted">{{ $permission->description }}</small>
                                                        @endif
                                                    </label>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                                <hr>
                            @endif
                        @endforeach
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn--primary w-100 h-45">@lang('Update Permissions')</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('breadcrumb-plugins')
    <a href="{{ route('admin.roles.index') }}" class="btn btn-sm btn--primary">
        <i class="la la-list"></i> @lang('All Roles')
    </a>
    <a href="{{ route('admin.roles.edit', $role->id) }}" class="btn btn-sm btn--secondary">
        <i class="la la-pencil"></i> @lang('Edit Role')
    </a>
@endpush

@push('script')
<script>
    'use strict';
    
    $(document).ready(function() {
        // Handle category toggle
        $('.category-toggle').on('change', function() {
            const category = $(this).data('category');
            const isChecked = $(this).is(':checked');
            
            $(`.permission-checkbox[data-category="${category}"]`).prop('checked', isChecked);
        });
        
        // Handle individual permission checkbox
        $('.permission-checkbox').on('change', function() {
            const category = $(this).data('category');
            const totalInCategory = $(`.permission-checkbox[data-category="${category}"]`).length;
            const checkedInCategory = $(`.permission-checkbox[data-category="${category}"]:checked`).length;
            
            const categoryToggle = $(`.category-toggle[data-category="${category}"]`);
            
            if (checkedInCategory === 0) {
                categoryToggle.prop('checked', false).prop('indeterminate', false);
            } else if (checkedInCategory === totalInCategory) {
                categoryToggle.prop('checked', true).prop('indeterminate', false);
            } else {
                categoryToggle.prop('checked', false).prop('indeterminate', true);
            }
        });
        
        // Initialize category toggles
        $('.category-toggle').each(function() {
            const category = $(this).data('category');
            const totalInCategory = $(`.permission-checkbox[data-category="${category}"]`).length;
            const checkedInCategory = $(`.permission-checkbox[data-category="${category}"]:checked`).length;
            
            if (checkedInCategory === 0) {
                $(this).prop('checked', false).prop('indeterminate', false);
            } else if (checkedInCategory === totalInCategory) {
                $(this).prop('checked', true).prop('indeterminate', false);
            } else {
                $(this).prop('checked', false).prop('indeterminate', true);
            }
        });
    });
</script>
@endpush

@push('style')
<style>
    .permission-category {
        border: 1px solid #e9ecef;
        border-radius: 0.375rem;
        padding: 1rem;
        background-color: #f8f9fa;
    }
    
    .form-check-label {
        cursor: pointer;
    }
    
    .form-check-input:indeterminate {
        background-color: #6c757d;
        border-color: #6c757d;
    }
</style>
@endpush
