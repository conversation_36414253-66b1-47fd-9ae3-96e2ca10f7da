# Admin Role System Troubleshooting Guide

## Common Issues and Solutions

### 1. 500 Error When Creating Roles

**Possible Causes:**
- Database tables not created (migrations not run)
- Missing permissions in database
- PHP/Laravel configuration issues
- Database connection problems

**Solutions:**

#### Option A: Run Migrations (Preferred)
```bash
cd public_html/core
php artisan migrate
php artisan db:seed --class=AdminPermissionSeeder
php artisan db:seed --class=AdminRoleSeeder
```

#### Option B: Manual Database Setup
If migrations fail, run the SQL script manually:
1. Access your database (phpMyAdmin, MySQL Workbench, etc.)
2. Run the SQL script from `docs/manual_admin_role_tables.sql`

#### Option C: Check Database Connection
1. Verify `.env` file has correct database credentials
2. Test database connection:
```bash
php artisan tinker
DB::connection()->getPdo();
```

### 2. "Table doesn't exist" Errors

**Check if tables exist:**
```sql
SHOW TABLES LIKE 'admin_%';
```

**Expected tables:**
- `admin_roles`
- `admin_permissions` 
- `admin_role_permissions`
- `admins` (should have new columns: `role_id`, `is_super_admin`, `status`)

**If tables missing:**
Run the manual SQL script or migrations as described above.

### 3. "Class not found" Errors

**Check if models exist:**
- `app/Models/AdminRole.php`
- `app/Models/AdminPermission.php`
- Updated `app/Models/Admin.php`

**If models missing:**
Re-create the model files from the implementation.

### 4. Permission Denied Errors

**Check admin permissions:**
```sql
SELECT a.name, a.is_super_admin, r.name as role_name 
FROM admins a 
LEFT JOIN admin_roles r ON a.role_id = r.id 
WHERE a.id = YOUR_ADMIN_ID;
```

**Make admin super admin:**
```sql
UPDATE admins SET is_super_admin = 1 WHERE id = 1;
```

### 5. Routes Not Working

**Check route registration:**
- Verify routes are in `routes/admin.php`
- Check middleware is properly registered in `bootstrap/app.php`

**Clear route cache:**
```bash
php artisan route:clear
php artisan config:clear
```

### 6. Views Not Found

**Check view files exist:**
- `resources/views/admin/staff/index.blade.php`
- `resources/views/admin/staff/create.blade.php`
- `resources/views/admin/roles/index.blade.php`
- `resources/views/admin/roles/create.blade.php`
- `resources/views/admin/roles/edit.blade.php`
- `resources/views/admin/roles/permissions.blade.php`

### 7. Menu Not Showing

**Check menu configuration:**
- Verify `resources/views/admin/partials/menu.json` has admin management section
- Clear view cache: `php artisan view:clear`

## Debug Steps

### Step 1: Check Database Tables
```sql
-- Check if admin_roles table exists
DESCRIBE admin_roles;

-- Check if admin_permissions table exists  
DESCRIBE admin_permissions;

-- Check if admins table has new columns
DESCRIBE admins;

-- Count permissions
SELECT COUNT(*) FROM admin_permissions;

-- Count roles
SELECT COUNT(*) FROM admin_roles;
```

### Step 2: Test Models in Tinker
```bash
php artisan tinker
```

```php
// Test AdminRole model
App\Models\AdminRole::count();

// Test AdminPermission model
App\Models\AdminPermission::count();

// Test relationships
$role = App\Models\AdminRole::first();
$role->permissions;

// Test admin model
$admin = App\Models\Admin::first();
$admin->isSuperAdmin();
```

### Step 3: Check Error Logs
```bash
# Check Laravel logs
tail -f storage/logs/laravel.log

# Check web server logs (Apache/Nginx)
# Location varies by server setup
```

### Step 4: Test Route Access
```bash
# List all routes
php artisan route:list | grep admin

# Test specific route
php artisan route:list | grep roles
```

## Quick Fix Commands

### Reset Everything
```bash
# Clear all caches
php artisan config:clear
php artisan route:clear
php artisan view:clear
php artisan cache:clear

# Re-run migrations (if safe)
php artisan migrate:fresh --seed
```

### Manual Permission Check
```sql
-- Check if current admin has super admin status
SELECT id, name, email, is_super_admin, status FROM admins WHERE id = 1;

-- Make first admin super admin
UPDATE admins SET is_super_admin = 1, status = 1 WHERE id = 1;
```

### Test Database Connection
```php
// In tinker
DB::select('SELECT 1');
```

## Error Messages and Solutions

### "SQLSTATE[42S02]: Base table or view not found"
- **Solution:** Run migrations or manual SQL script

### "Class 'App\Models\AdminRole' not found"
- **Solution:** Ensure model files exist and are properly namespaced

### "Route [admin.roles.index] not defined"
- **Solution:** Check route registration and clear route cache

### "View [admin.roles.index] not found"
- **Solution:** Create missing view files

### "Call to undefined method"
- **Solution:** Check model relationships and method names

## Prevention Tips

1. **Always backup database** before running migrations
2. **Test in development** environment first
3. **Check PHP version compatibility** (Laravel requirements)
4. **Verify file permissions** on storage and cache directories
5. **Use version control** to track changes

## Getting Help

If issues persist:

1. **Check Laravel logs** for detailed error messages
2. **Enable debug mode** in `.env`: `APP_DEBUG=true`
3. **Test with minimal data** (single role, single permission)
4. **Verify PHP extensions** are installed (PDO, MySQL, etc.)
5. **Check server requirements** for Laravel

## Contact Information

For additional support, provide:
- Error message (full stack trace)
- Laravel version
- PHP version
- Database type and version
- Steps to reproduce the issue
