@extends('admin.layouts.app')

@section('panel')
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <form action="{{ route('admin.roles.update', $role->id) }}" method="POST">
                    @csrf
                    @method('PUT')
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">@lang('Role Name') <span class="text--danger">*</span></label>
                                    <input type="text" class="form-control" name="name" value="{{ old('name', $role->name) }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">@lang('Status') <span class="text--danger">*</span></label>
                                    <select name="status" class="form-control" required>
                                        <option value="1" {{ old('status', $role->status) == '1' ? 'selected' : '' }}>@lang('Active')</option>
                                        <option value="0" {{ old('status', $role->status) == '0' ? 'selected' : '' }}>@lang('Inactive')</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label class="form-label">@lang('Description')</label>
                                    <textarea class="form-control" name="description" rows="3">{{ old('description', $role->description) }}</textarea>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="mb-3">@lang('Assign Permissions')</h5>
                                @foreach($categories as $categoryKey => $categoryName)
                                    @if(isset($permissions[$categoryKey]) && $permissions[$categoryKey]->count() > 0)
                                        <div class="permission-category mb-4">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h6 class="mb-0">{{ $categoryName }}</h6>
                                                <div class="form-check">
                                                    <input class="form-check-input category-toggle" type="checkbox" 
                                                           id="category_{{ $categoryKey }}" 
                                                           data-category="{{ $categoryKey }}">
                                                    <label class="form-check-label" for="category_{{ $categoryKey }}">
                                                        @lang('Select All')
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="row">
                                                @foreach($permissions[$categoryKey] as $permission)
                                                    <div class="col-md-6 col-lg-4">
                                                        <div class="form-check mb-2">
                                                            <input class="form-check-input permission-checkbox" 
                                                                   type="checkbox" 
                                                                   name="permissions[]" 
                                                                   value="{{ $permission->id }}" 
                                                                   id="permission_{{ $permission->id }}"
                                                                   data-category="{{ $categoryKey }}"
                                                                   {{ in_array($permission->id, old('permissions', $rolePermissions)) ? 'checked' : '' }}>
                                                            <label class="form-check-label" for="permission_{{ $permission->id }}">
                                                                <strong>{{ $permission->name }}</strong>
                                                                @if($permission->description)
                                                                    <br><small class="text-muted">{{ $permission->description }}</small>
                                                                @endif
                                                            </label>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                        <hr>
                                    @endif
                                @endforeach

                                @if(empty($permissions) || $permissions->flatten()->count() == 0)
                                    <div class="alert alert--warning">
                                        <p class="mb-0">@lang('No permissions available. Please run the permission seeder first.')</p>
                                        <small>@lang('Run: php artisan db:seed --class=AdminPermissionSeeder')</small>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn--primary w-100 h-45">@lang('Update Role')</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('breadcrumb-plugins')
    <a href="{{ route('admin.roles.index') }}" class="btn btn-sm btn--primary">
        <i class="la la-list"></i> @lang('All Roles')
    </a>
    <a href="{{ route('admin.roles.permissions', $role->id) }}" class="btn btn-sm btn--secondary">
        <i class="la la-key"></i> @lang('Manage Permissions')
    </a>
@endpush

@push('script')
<script>
    'use strict';
    
    $(document).ready(function() {
        // Handle category toggle
        $('.category-toggle').on('change', function() {
            const category = $(this).data('category');
            const isChecked = $(this).is(':checked');
            
            $(`.permission-checkbox[data-category="${category}"]`).prop('checked', isChecked);
        });
        
        // Handle individual permission checkbox
        $('.permission-checkbox').on('change', function() {
            const category = $(this).data('category');
            const totalInCategory = $(`.permission-checkbox[data-category="${category}"]`).length;
            const checkedInCategory = $(`.permission-checkbox[data-category="${category}"]:checked`).length;
            
            const categoryToggle = $(`.category-toggle[data-category="${category}"]`);
            
            if (checkedInCategory === 0) {
                categoryToggle.prop('checked', false).prop('indeterminate', false);
            } else if (checkedInCategory === totalInCategory) {
                categoryToggle.prop('checked', true).prop('indeterminate', false);
            } else {
                categoryToggle.prop('checked', false).prop('indeterminate', true);
            }
        });
        
        // Initialize category toggles
        $('.category-toggle').each(function() {
            const category = $(this).data('category');
            const totalInCategory = $(`.permission-checkbox[data-category="${category}"]`).length;
            const checkedInCategory = $(`.permission-checkbox[data-category="${category}"]:checked`).length;
            
            if (checkedInCategory === 0) {
                $(this).prop('checked', false).prop('indeterminate', false);
            } else if (checkedInCategory === totalInCategory) {
                $(this).prop('checked', true).prop('indeterminate', false);
            } else {
                $(this).prop('checked', false).prop('indeterminate', true);
            }
        });
    });
</script>
@endpush

@push('style')
<style>
    .permission-category {
        border: 1px solid #e9ecef;
        border-radius: 0.375rem;
        padding: 1rem;
        background-color: #f8f9fa;
    }
    
    .form-check-label {
        cursor: pointer;
    }
    
    .form-check-input:indeterminate {
        background-color: #6c757d;
        border-color: #6c757d;
    }
</style>
@endpush
