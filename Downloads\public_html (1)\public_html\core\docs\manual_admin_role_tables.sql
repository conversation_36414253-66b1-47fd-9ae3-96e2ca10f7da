-- Manual SQL script to create admin role system tables
-- Run this if Lara<PERSON> migrations are not working

-- Create admin_roles table
CREATE TABLE IF NOT EXISTS `admin_roles` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `admin_roles_slug_unique` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create admin_permissions table
CREATE TABLE IF NOT EXISTS `admin_permissions` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `category` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `admin_permissions_slug_unique` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create admin_role_permissions pivot table
CREATE TABLE IF NOT EXISTS `admin_role_permissions` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `role_id` bigint(20) UNSIGNED NOT NULL,
  `permission_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `admin_role_permissions_role_id_permission_id_unique` (`role_id`,`permission_id`),
  KEY `admin_role_permissions_role_id_foreign` (`role_id`),
  KEY `admin_role_permissions_permission_id_foreign` (`permission_id`),
  CONSTRAINT `admin_role_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `admin_permissions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `admin_role_permissions_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `admin_roles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add role fields to admins table
ALTER TABLE `admins` 
ADD COLUMN `role_id` bigint(20) UNSIGNED NULL AFTER `email`,
ADD COLUMN `is_super_admin` tinyint(1) NOT NULL DEFAULT 0 AFTER `role_id`,
ADD COLUMN `status` tinyint(1) NOT NULL DEFAULT 1 AFTER `is_super_admin`;

-- Add foreign key constraint for role_id
ALTER TABLE `admins` 
ADD CONSTRAINT `admins_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `admin_roles` (`id`) ON DELETE SET NULL;

-- Insert default permissions
INSERT INTO `admin_permissions` (`name`, `slug`, `category`, `description`, `status`, `created_at`, `updated_at`) VALUES
('View Drivers', 'driver_management', 'driver_management', 'View and manage drivers', 1, NOW(), NOW()),
('Driver Verification', 'driver_verification', 'driver_management', 'Approve/reject driver verification', 1, NOW(), NOW()),
('Vehicle Verification', 'vehicle_verification', 'driver_management', 'Approve/reject vehicle verification', 1, NOW(), NOW()),
('View Riders', 'rider_management', 'rider_management', 'View and manage riders', 1, NOW(), NOW()),
('Rider Verification', 'rider_verification', 'rider_management', 'Manage rider verification', 1, NOW(), NOW()),
('View Rides', 'ride_management', 'ride_management', 'View and manage rides', 1, NOW(), NOW()),
('Ride Analytics', 'ride_analytics', 'ride_management', 'View ride analytics and reports', 1, NOW(), NOW()),
('View Payments', 'payment_management', 'payment_management', 'View payment transactions', 1, NOW(), NOW()),
('Manage Deposits', 'deposit_management', 'payment_management', 'Manage user deposits', 1, NOW(), NOW()),
('Manage Withdrawals', 'withdrawal_management', 'payment_management', 'Manage withdrawal requests', 1, NOW(), NOW()),
('General Settings', 'general_settings', 'general_settings', 'Manage general application settings', 1, NOW(), NOW()),
('Language Management', 'language_management', 'general_settings', 'Manage application languages', 1, NOW(), NOW()),
('SEO Settings', 'seo_settings', 'general_settings', 'Manage SEO settings', 1, NOW(), NOW()),
('View Reports', 'reports_analytics', 'reports_analytics', 'View system reports and analytics', 1, NOW(), NOW()),
('Financial Reports', 'financial_reports', 'reports_analytics', 'View financial reports', 1, NOW(), NOW()),
('System Configuration', 'system_configuration', 'system_configuration', 'Manage system configuration', 1, NOW(), NOW()),
('Maintenance Mode', 'maintenance_mode', 'system_configuration', 'Control maintenance mode', 1, NOW(), NOW()),
('Cron Jobs', 'cron_jobs', 'system_configuration', 'Manage cron jobs', 1, NOW(), NOW()),
('Admin Management', 'admin_management', 'admin_management', 'Manage admin users', 1, NOW(), NOW()),
('Role Management', 'role_management', 'admin_management', 'Manage admin roles and permissions', 1, NOW(), NOW());

-- Insert default roles
INSERT INTO `admin_roles` (`name`, `slug`, `description`, `status`, `created_at`, `updated_at`) VALUES
('Driver Manager', 'driver-manager', 'Manages all driver-related operations including verification and vehicle management', 1, NOW(), NOW()),
('Rider Manager', 'rider-manager', 'Manages all rider-related operations and verification', 1, NOW(), NOW()),
('Finance Manager', 'finance-manager', 'Manages all financial operations including deposits and withdrawals', 1, NOW(), NOW()),
('Operations Manager', 'operations-manager', 'Manages rides and general operations', 1, NOW(), NOW()),
('Content Manager', 'content-manager', 'Manages general settings and content', 1, NOW(), NOW()),
('System Administrator', 'system-administrator', 'Manages system configuration and technical settings', 1, NOW(), NOW());

-- Assign permissions to Driver Manager role
INSERT INTO `admin_role_permissions` (`role_id`, `permission_id`, `created_at`, `updated_at`)
SELECT r.id, p.id, NOW(), NOW()
FROM `admin_roles` r, `admin_permissions` p
WHERE r.slug = 'driver-manager' 
AND p.slug IN ('driver_management', 'driver_verification', 'vehicle_verification');

-- Assign permissions to Rider Manager role
INSERT INTO `admin_role_permissions` (`role_id`, `permission_id`, `created_at`, `updated_at`)
SELECT r.id, p.id, NOW(), NOW()
FROM `admin_roles` r, `admin_permissions` p
WHERE r.slug = 'rider-manager' 
AND p.slug IN ('rider_management', 'rider_verification');

-- Assign permissions to Finance Manager role
INSERT INTO `admin_role_permissions` (`role_id`, `permission_id`, `created_at`, `updated_at`)
SELECT r.id, p.id, NOW(), NOW()
FROM `admin_roles` r, `admin_permissions` p
WHERE r.slug = 'finance-manager' 
AND p.slug IN ('payment_management', 'deposit_management', 'withdrawal_management', 'financial_reports');

-- Assign permissions to Operations Manager role
INSERT INTO `admin_role_permissions` (`role_id`, `permission_id`, `created_at`, `updated_at`)
SELECT r.id, p.id, NOW(), NOW()
FROM `admin_roles` r, `admin_permissions` p
WHERE r.slug = 'operations-manager' 
AND p.slug IN ('ride_management', 'ride_analytics', 'reports_analytics');

-- Assign permissions to Content Manager role
INSERT INTO `admin_role_permissions` (`role_id`, `permission_id`, `created_at`, `updated_at`)
SELECT r.id, p.id, NOW(), NOW()
FROM `admin_roles` r, `admin_permissions` p
WHERE r.slug = 'content-manager' 
AND p.slug IN ('general_settings', 'language_management', 'seo_settings');

-- Assign permissions to System Administrator role
INSERT INTO `admin_role_permissions` (`role_id`, `permission_id`, `created_at`, `updated_at`)
SELECT r.id, p.id, NOW(), NOW()
FROM `admin_roles` r, `admin_permissions` p
WHERE r.slug = 'system-administrator' 
AND p.slug IN ('system_configuration', 'maintenance_mode', 'cron_jobs');

-- Update first admin to be super admin
UPDATE `admins` SET `is_super_admin` = 1, `status` = 1 WHERE `id` = 1;

-- Create a sample staff admin for testing (optional)
-- INSERT INTO `admins` (`name`, `username`, `email`, `password`, `role_id`, `is_super_admin`, `status`, `created_at`, `updated_at`)
-- SELECT 'Staff Admin', 'staff_admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', r.id, 0, 1, NOW(), NOW()
-- FROM `admin_roles` r WHERE r.slug = 'driver-manager' LIMIT 1;
