-- Fix NULL values in admin table that cause 500 errors

-- Update NULL is_super_admin values to 0 (false)
UPDATE admins SET is_super_admin = 0 WHERE is_super_admin IS NULL;

-- Update NULL status values to 1 (active)
UPDATE admins SET status = 1 WHERE status IS NULL;

-- Make the first admin a super admin (if not already)
UPDATE admins SET is_super_admin = 1 WHERE id = 1;

-- Ensure all admins have a status (active by default)
UPDATE admins SET status = 1 WHERE status IS NULL OR status = 0;

-- Show the updated admin data
SELECT id, name, email, username, 
       COALESCE(is_super_admin, 0) as is_super_admin,
       COALESCE(status, 1) as status,
       role_id
FROM admins 
ORDER BY id;

-- Optional: Add NOT NULL constraints to prevent future NULL values
-- ALTER TABLE admins MODIFY COLUMN is_super_admin tinyint(1) NOT NULL DEFAULT 0;
-- ALTER TABLE admins MODIFY COLUMN status tinyint(1) NOT NULL DEFAULT 1;
