@extends('admin.layouts.app')

@section('panel')
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <form action="{{ route('admin.staff.update', $admin->id) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">@lang('Name') <span class="text--danger">*</span></label>
                                    <input type="text" class="form-control" name="name" value="{{ old('name', $admin->name) }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">@lang('Username') <span class="text--danger">*</span></label>
                                    <input type="text" class="form-control" name="username" value="{{ old('username', $admin->username) }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">@lang('Email') <span class="text--danger">*</span></label>
                                    <input type="email" class="form-control" name="email" value="{{ old('email', $admin->email) }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">@lang('Password')</label>
                                    <input type="password" class="form-control" name="password" placeholder="@lang('Leave blank to keep current password')">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">@lang('Confirm Password')</label>
                                    <input type="password" class="form-control" name="password_confirmation" placeholder="@lang('Confirm new password')">
                                </div>
                            </div>
                            @if($roles->count() > 0)
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">@lang('Role') <span class="text--danger">*</span></label>
                                        <select name="role_id" class="form-control" required>
                                            <option value="">@lang('Select Role')</option>
                                            @foreach($roles as $role)
                                                <option value="{{ $role->id }}" {{ old('role_id', $admin->role_id) == $role->id ? 'selected' : '' }}>
                                                    {{ $role->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            @else
                                <div class="col-md-6">
                                    <div class="alert alert--warning">
                                        <p class="mb-0">@lang('No roles available. Please create roles first.')</p>
                                        <small>
                                            <a href="{{ route('admin.roles.index') }}">@lang('Manage Roles')</a>
                                        </small>
                                    </div>
                                </div>
                            @endif
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">@lang('Status') <span class="text--danger">*</span></label>
                                    <select name="status" class="form-control" required>
                                        <option value="1" {{ old('status', $admin->status ?? 1) == '1' ? 'selected' : '' }}>@lang('Active')</option>
                                        <option value="0" {{ old('status', $admin->status ?? 1) == '0' ? 'selected' : '' }}>@lang('Inactive')</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">@lang('Profile Image')</label>
                                    <div class="image-upload">
                                        <div class="thumb">
                                            <div class="avatar-preview">
                                                <div class="profilePicPreview" style="background-image: url({{ $admin->imageSrc }})">
                                                    <button type="button" class="remove-image"><i class="fa fa-times"></i></button>
                                                </div>
                                            </div>
                                            <div class="avatar-edit">
                                                <input type="file" class="profilePicUpload" name="image" id="profilePicUpload1" accept=".png, .jpg, .jpeg">
                                                <label for="profilePicUpload1" class="bg--success">@lang('Upload Image')</label>
                                                <small class="mt-2 text-facebook">@lang('Supported files'): <b>@lang('jpeg, jpg, png')</b>. @lang('Image will be resized into 400x400px')</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn--primary w-100 h-45">@lang('Update Admin')</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('breadcrumb-plugins')
    <a href="{{ route('admin.staff.index') }}" class="btn btn-sm btn--primary">
        <i class="la la-list"></i> @lang('All Admins')
    </a>
    @if($admin->role)
        <a href="{{ route('admin.staff.permissions', $admin->id) }}" class="btn btn-sm btn--secondary">
            <i class="la la-key"></i> @lang('View Permissions')
        </a>
    @endif
@endpush

@push('style')
<style>
    .image-upload .thumb .profilePicPreview {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        background-size: cover;
        background-position: center;
        border: 3px solid #f1f1f1;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .image-upload .remove-image {
        position: absolute;
        top: -5px;
        right: -5px;
        background: #dc3545;
        color: white;
        border: none;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        font-size: 10px;
        cursor: pointer;
    }
</style>
@endpush

@push('script')
<script>
    'use strict';
    
    $(document).ready(function() {
        // Image upload preview
        function readURL(input) {
            if (input.files && input.files[0]) {
                var reader = new FileReader();
                reader.onload = function(e) {
                    $('.profilePicPreview').css('background-image', 'url(' + e.target.result + ')');
                    $('.profilePicPreview').hide();
                    $('.profilePicPreview').fadeIn(650);
                }
                reader.readAsDataURL(input.files[0]);
            }
        }
        
        $(".profilePicUpload").change(function() {
            readURL(this);
        });
        
        // Remove image
        $('.remove-image').on('click', function() {
            $('.profilePicPreview').css('background-image', 'url({{ asset("assets/images/default.png") }})');
            $('.profilePicUpload').val('');
        });
    });
</script>
@endpush
