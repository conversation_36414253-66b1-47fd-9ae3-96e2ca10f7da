<?php
/**
 * Simple script to test admin authentication
 * Place this in public_html/core/ and run via browser: yourdomain.com/test_admin_auth.php
 */

// Include Laravel bootstrap
require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    echo "<h2>Admin Authentication Test</h2>";
    
    // Test database connection
    echo "<h3>1. Database Connection Test</h3>";
    try {
        $pdo = DB::connection()->getPdo();
        echo "✅ Database connection successful<br>";
    } catch (Exception $e) {
        echo "❌ Database connection failed: " . $e->getMessage() . "<br>";
        exit;
    }
    
    // Check if admins table exists
    echo "<h3>2. Admins Table Check</h3>";
    try {
        $admins = DB::table('admins')->count();
        echo "✅ Admins table exists with {$admins} records<br>";
    } catch (Exception $e) {
        echo "❌ Admins table error: " . $e->getMessage() . "<br>";
        exit;
    }
    
    // Check table structure
    echo "<h3>3. Admins Table Structure</h3>";
    try {
        $columns = DB::select("DESCRIBE admins");
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column->Field}</td>";
            echo "<td>{$column->Type}</td>";
            echo "<td>{$column->Null}</td>";
            echo "<td>{$column->Key}</td>";
            echo "<td>{$column->Default}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } catch (Exception $e) {
        echo "❌ Error getting table structure: " . $e->getMessage() . "<br>";
    }
    
    // Check admin records
    echo "<h3>4. Admin Records</h3>";
    try {
        $admins = DB::table('admins')->select('id', 'name', 'email', 'username')->get();
        if ($admins->count() > 0) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Username</th></tr>";
            foreach ($admins as $admin) {
                echo "<tr>";
                echo "<td>{$admin->id}</td>";
                echo "<td>{$admin->name}</td>";
                echo "<td>{$admin->email}</td>";
                echo "<td>{$admin->username}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "❌ No admin records found<br>";
        }
    } catch (Exception $e) {
        echo "❌ Error getting admin records: " . $e->getMessage() . "<br>";
    }
    
    // Test Admin model
    echo "<h3>5. Admin Model Test</h3>";
    try {
        $admin = App\Models\Admin::first();
        if ($admin) {
            echo "✅ Admin model working<br>";
            echo "First admin: {$admin->name} ({$admin->email})<br>";
            
            // Test new methods if they exist
            if (method_exists($admin, 'isSuperAdmin')) {
                echo "isSuperAdmin(): " . ($admin->isSuperAdmin() ? 'Yes' : 'No') . "<br>";
            }
            
            if (method_exists($admin, 'hasPermission')) {
                echo "hasPermission() method exists<br>";
            }
            
        } else {
            echo "❌ No admin found in database<br>";
        }
    } catch (Exception $e) {
        echo "❌ Admin model error: " . $e->getMessage() . "<br>";
    }
    
    // Test authentication
    echo "<h3>6. Authentication Test</h3>";
    try {
        $admin = App\Models\Admin::first();
        if ($admin) {
            // Test login
            Auth::guard('admin')->login($admin);
            if (Auth::guard('admin')->check()) {
                echo "✅ Authentication successful<br>";
                echo "Logged in as: " . Auth::guard('admin')->user()->name . "<br>";
                Auth::guard('admin')->logout();
            } else {
                echo "❌ Authentication failed<br>";
            }
        }
    } catch (Exception $e) {
        echo "❌ Authentication test error: " . $e->getMessage() . "<br>";
    }
    
    echo "<h3>7. Recommendations</h3>";
    echo "<ul>";
    echo "<li>If you see missing columns (role_id, is_super_admin, status), run the fix_admin_authentication.sql script</li>";
    echo "<li>If authentication is failing, check your .env database credentials</li>";
    echo "<li>If admin model errors occur, ensure all model files are properly uploaded</li>";
    echo "<li>Clear Laravel cache: php artisan config:clear && php artisan route:clear</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "❌ General error: " . $e->getMessage() . "<br>";
    echo "Stack trace: <pre>" . $e->getTraceAsString() . "</pre>";
}
?>
