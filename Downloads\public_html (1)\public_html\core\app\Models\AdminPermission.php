<?php

namespace App\Models;

use App\Traits\GlobalStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;

class AdminPermission extends Model
{
    use GlobalStatus;

    protected $fillable = [
        'name',
        'slug',
        'category',
        'description',
        'status'
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    /**
     * Get the roles that have this permission.
     */
    public function roles()
    {
        return $this->belongsToMany(AdminRole::class, 'admin_role_permissions', 'permission_id', 'role_id');
    }

    /**
     * Scope to filter by category
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Get permission categories
     */
    public static function getCategories()
    {
        return [
            'main' => 'Main',
            'people' => 'People',
            'finance' => 'Finance',
            'verification' => 'Verification',
            'system_report' => 'System Report',
            'system_utilities' => 'System Utilities',
            'admin_management' => 'Admin Management',
            'settings' => 'Settings',
            'frontend_manager' => 'Frontend Manager',
            'other' => 'Other',
        ];
    }

    /**
     * Get category name
     */
    public function categoryName(): Attribute
    {
        return new Attribute(
            get: fn() => self::getCategories()[$this->category] ?? ucfirst(str_replace('_', ' ', $this->category)),
        );
    }

    /**
     * Get permission badge for display
     */
    public function permissionBadge(): Attribute
    {
        $colors = [
            'driver_management' => 'success',
            'rider_management' => 'info',
            'ride_management' => 'warning',
            'payment_management' => 'primary',
            'general_settings' => 'secondary',
            'reports_analytics' => 'dark',
            'system_configuration' => 'danger',
            'admin_management' => 'primary',
        ];

        $color = $colors[$this->category] ?? 'secondary';

        return new Attribute(
            get: fn() => '<span class="badge badge--' . $color . '">' . $this->categoryName . '</span>',
        );
    }
}
