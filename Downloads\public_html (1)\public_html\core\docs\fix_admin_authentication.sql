-- Quick fix for admin authentication issues
-- Run this SQL to add missing columns to admins table

-- Check if columns exist and add them if they don't
SET @sql = '';

-- Add role_id column if it doesn't exist
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND table_name = 'admins' 
AND column_name = 'role_id';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE admins ADD COLUMN role_id bigint(20) UNSIGNED NULL AFTER email;', 
    'SELECT "role_id column already exists" as message;');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add is_super_admin column if it doesn't exist
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND table_name = 'admins' 
AND column_name = 'is_super_admin';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE admins ADD COLUMN is_super_admin tinyint(1) NOT NULL DEFAULT 0 AFTER role_id;', 
    'SELECT "is_super_admin column already exists" as message;');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add status column if it doesn't exist
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND table_name = 'admins' 
AND column_name = 'status';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE admins ADD COLUMN status tinyint(1) NOT NULL DEFAULT 1 AFTER is_super_admin;', 
    'SELECT "status column already exists" as message;');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Make first admin super admin and active
UPDATE admins SET is_super_admin = 1, status = 1 WHERE id = 1;

-- Show current admin structure
SELECT 'Current admins table structure:' as message;
DESCRIBE admins;

-- Show current admin data
SELECT 'Current admin data:' as message;
SELECT id, name, email, username, role_id, is_super_admin, status FROM admins;
