@extends('admin.layouts.app')

@section('panel')
    <div class="row">
        <div class="col-lg-12">
            <div class="card b-radius--10">
                <div class="card-body p-0">
                    <div class="table-responsive--md table-responsive">
                        <table class="table--light style--two table">
                            <thead>
                                <tr>
                                    <th>@lang('Role Name')</th>
                                    <th>@lang('Description')</th>
                                    <th>@lang('Admins Count')</th>
                                    <th>@lang('Permissions Count')</th>
                                    <th>@lang('Status')</th>
                                    <th>@lang('Action')</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($roles as $role)
                                    <tr>
                                        <td>
                                            <span class="fw-bold">{{ $role->name }}</span><br>
                                            <span class="small text-muted">{{ $role->slug }}</span>
                                        </td>
                                        <td>
                                            <span>{{ Str::limit($role->description, 50) ?? 'N/A' }}</span>
                                        </td>
                                        <td>
                                            <span class="badge badge--primary">{{ $role->admins_count }}</span>
                                        </td>
                                        <td>
                                            <span class="badge badge--info">{{ $role->permissions_count }}</span>
                                        </td>
                                        <td>
                                            {!! $role->statusBadge !!}
                                        </td>
                                        <td>
                                            <div class="button--group">
                                                <a href="{{ route('admin.roles.edit', $role->id) }}" class="btn btn-sm btn-outline--primary">
                                                    <i class="la la-pencil"></i> @lang('Edit')
                                                </a>
                                                
                                                <a href="{{ route('admin.roles.permissions', $role->id) }}" class="btn btn-sm btn-outline--info">
                                                    <i class="la la-key"></i> @lang('Permissions')
                                                </a>

                                                @if($role->status)
                                                    <button class="btn btn-sm btn-outline--danger confirmationBtn" 
                                                            data-action="{{ route('admin.roles.status', $role->id) }}" 
                                                            data-question="@lang('Are you sure to disable this role?')">
                                                        <i class="la la-eye-slash"></i> @lang('Disable')
                                                    </button>
                                                @else
                                                    <button class="btn btn-sm btn-outline--success confirmationBtn" 
                                                            data-action="{{ route('admin.roles.status', $role->id) }}" 
                                                            data-question="@lang('Are you sure to enable this role?')">
                                                        <i class="la la-eye"></i> @lang('Enable')
                                                    </button>
                                                @endif

                                                @if($role->admins_count == 0)
                                                    <button class="btn btn-sm btn-outline--danger confirmationBtn"
                                                            data-action="{{ route('admin.roles.delete', $role->id) }}"
                                                            data-question="@lang('Are you sure to delete this role?')"
                                                            data-method="DELETE">
                                                        <i class="la la-trash"></i> @lang('Delete')
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td class="text-muted text-center" colspan="100%">{{ __($emptyMessage) }}</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
                @if ($roles->hasPages())
                    <div class="card-footer py-4">
                        {{ paginateLinks($roles) }}
                    </div>
                @endif
            </div>
        </div>
    </div>

    <x-confirmation-modal />
@endsection

@push('breadcrumb-plugins')
    <a href="{{ route('admin.roles.create') }}" class="btn btn-sm btn--primary">
        <i class="la la-plus"></i>@lang('Add New Role')
    </a>
    <a href="{{ route('admin.staff.index') }}" class="btn btn-sm btn--secondary">
        <i class="la la-users"></i>@lang('Manage Admins')
    </a>
@endpush
