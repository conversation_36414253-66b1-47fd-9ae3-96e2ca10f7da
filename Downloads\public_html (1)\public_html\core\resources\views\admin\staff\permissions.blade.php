@extends('admin.layouts.app')

@section('panel')
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">@lang('Admin Permissions')</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="admin-info">
                                <h6>@lang('Admin Details')</h6>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>@lang('Name'):</strong></td>
                                        <td>{{ $admin->name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>@lang('Email'):</strong></td>
                                        <td>{{ $admin->email }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>@lang('Username'):</strong></td>
                                        <td>{{ $admin->username }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>@lang('Status'):</strong></td>
                                        <td>{!! $admin->statusBadge !!}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>@lang('Role'):</strong></td>
                                        <td>{!! $admin->roleBadge !!}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="admin-image text-center">
                                <img src="{{ $admin->imageSrc }}" alt="@lang('Admin Image')" class="admin-profile-image">
                            </div>
                        </div>
                    </div>

                    <hr>

                    @if($admin->isSuperAdmin())
                        <div class="alert alert--success">
                            <h5><i class="la la-crown"></i> @lang('Super Administrator')</h5>
                            <p class="mb-0">@lang('This admin has super administrator privileges and can access all features of the system.')</p>
                        </div>
                    @elseif($admin->role && $admin->role->permissions)
                        <div class="permissions-section">
                            <h6>@lang('Role Permissions')</h6>
                            <p class="text-muted">@lang('This admin has the following permissions through their role'): <strong>{{ $admin->role->name }}</strong></p>
                            
                            @php
                                $groupedPermissions = $admin->role->permissions->groupBy('category');
                            @endphp

                            @if($groupedPermissions->count() > 0)
                                <div class="row">
                                    @foreach($groupedPermissions as $category => $permissions)
                                        <div class="col-md-6 col-lg-4 mb-4">
                                            <div class="permission-category">
                                                <h6 class="category-title">{{ ucwords(str_replace('_', ' ', $category)) }}</h6>
                                                <ul class="permission-list">
                                                    @foreach($permissions as $permission)
                                                        <li>
                                                            <i class="la la-check text-success"></i>
                                                            <span>{{ $permission->name }}</span>
                                                            @if($permission->description)
                                                                <small class="text-muted d-block">{{ $permission->description }}</small>
                                                            @endif
                                                        </li>
                                                    @endforeach
                                                </ul>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <div class="alert alert--warning">
                                    <p class="mb-0">@lang('This role has no permissions assigned.')</p>
                                </div>
                            @endif
                        </div>
                    @elseif($admin->role)
                        <div class="alert alert--info">
                            <h6>@lang('Role Information')</h6>
                            <p><strong>@lang('Role'):</strong> {{ $admin->role->name }}</p>
                            @if($admin->role->description)
                                <p><strong>@lang('Description'):</strong> {{ $admin->role->description }}</p>
                            @endif
                            <p class="mb-0">@lang('Permission details are not available at this time.')</p>
                        </div>
                    @else
                        <div class="alert alert--warning">
                            <h6>@lang('No Role Assigned')</h6>
                            <p class="mb-0">@lang('This admin does not have a role assigned. Please assign a role to grant permissions.')</p>
                            <a href="{{ route('admin.staff.edit', $admin->id) }}" class="btn btn-sm btn--primary mt-2">
                                <i class="la la-edit"></i> @lang('Edit Admin')
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('breadcrumb-plugins')
    <a href="{{ route('admin.staff.index') }}" class="btn btn-sm btn--primary">
        <i class="la la-list"></i> @lang('All Admins')
    </a>
    <a href="{{ route('admin.staff.edit', $admin->id) }}" class="btn btn-sm btn--secondary">
        <i class="la la-edit"></i> @lang('Edit Admin')
    </a>
    @if($admin->role)
        <a href="{{ route('admin.roles.edit', $admin->role->id) }}" class="btn btn-sm btn--info">
            <i class="la la-key"></i> @lang('Edit Role')
        </a>
    @endif
@endpush

@push('style')
<style>
    .admin-profile-image {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid #f1f1f1;
    }
    
    .permission-category {
        border: 1px solid #e9ecef;
        border-radius: 0.375rem;
        padding: 1rem;
        background-color: #f8f9fa;
        height: 100%;
    }
    
    .category-title {
        color: #495057;
        margin-bottom: 0.75rem;
        font-weight: 600;
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 0.5rem;
    }
    
    .permission-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .permission-list li {
        padding: 0.25rem 0;
        display: flex;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .permission-list li i {
        margin-top: 0.125rem;
        flex-shrink: 0;
    }
    
    .permission-list li span {
        font-weight: 500;
    }
    
    .admin-info table td {
        padding: 0.5rem 0;
        border: none;
    }
    
    .admin-info table td:first-child {
        width: 100px;
    }
</style>
@endpush
